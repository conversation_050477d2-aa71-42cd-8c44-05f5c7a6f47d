#!/usr/bin/env python3
"""
كاشف اللحظات المثيرة المبسط
Simple Highlight Detector

نظام مبسط وفعال لاكتشاف اللحظات المثيرة والمضحكة في الفيديوهات
يركز على النتائج العملية بدلاً من التعقيد التقني
"""

import cv2
import numpy as np
import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
import statistics
import json

logger = logging.getLogger(__name__)

@dataclass
class SimpleHighlight:
    """لحظة مثيرة مبسطة"""
    start_time: float
    end_time: float
    peak_time: float
    highlight_type: str
    excitement_score: float
    description: str
    reasons: List[str]

class SimpleHighlightDetector:
    """كاشف اللحظات المثيرة المبسط"""
    
    def __init__(self):
        # إعدادات بسيطة وفعالة
        self.min_clip_duration = 8.0   # 8 ثوانٍ كحد أدنى
        self.max_clip_duration = 45.0  # 45 ثانية كحد أقصى
        self.optimal_duration = 25.0   # 25 ثانية مثالية للشورتس
        
        # عتبات الكشف
        self.motion_threshold = 30.0    # عتبة الحركة
        self.brightness_change_threshold = 0.15  # تغيير السطوع
        self.scene_change_threshold = 0.3  # تغيير المشهد
        
        # أنواع اللحظات المثيرة
        self.highlight_types = {
            'high_action': 'لحظة حركة عالية',
            'scene_change': 'تغيير مشهد مثير',
            'bright_moment': 'لحظة مضيئة مثيرة',
            'fast_motion': 'حركة سريعة',
            'dramatic_change': 'تغيير دراماتيكي',
            'peak_moment': 'ذروة مثيرة',
            'funny_moment': 'لحظة مضحكة محتملة',
            'surprise_moment': 'لحظة مفاجئة'
        }
        
        logger.info("تم تهيئة كاشف اللحظات المثيرة المبسط")

    def detect_exciting_moments(self, video_path: str, target_clips: int = 3) -> List[SimpleHighlight]:
        """كشف اللحظات المثيرة في الفيديو"""
        try:
            logger.info(f"🔍 بدء كشف اللحظات المثيرة: {video_path}")
            
            # فتح الفيديو
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                logger.error("لا يمكن فتح الفيديو")
                return []
            
            # معلومات الفيديو
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / fps
            
            logger.info(f"📊 مدة الفيديو: {duration:.1f}s، FPS: {fps:.1f}")
            
            # تحليل سريع وفعال
            exciting_moments = self._analyze_video_for_excitement(cap, fps, duration)
            
            cap.release()
            
            # اختيار أفضل اللحظات
            best_moments = self._select_best_moments(exciting_moments, target_clips)
            
            # تحسين المقاطع
            optimized_clips = self._optimize_clips(best_moments, duration)
            
            logger.info(f"✅ تم اكتشاف {len(optimized_clips)} لحظة مثيرة")
            return optimized_clips
            
        except Exception as e:
            logger.error(f"❌ خطأ في كشف اللحظات المثيرة: {e}")
            return []

    def _analyze_video_for_excitement(self, cap: cv2.VideoCapture, fps: float, duration: float) -> List[Dict[str, Any]]:
        """تحليل الفيديو للبحث عن اللحظات المثيرة"""
        try:
            exciting_moments = []
            
            # تحليل كل 5 ثوانٍ
            analysis_interval = 5.0
            window_size = 3.0  # نافذة 3 ثوانٍ للتحليل
            
            for start_time in np.arange(0, duration - window_size, analysis_interval):
                end_time = min(start_time + window_size, duration)
                
                # تحليل النافذة
                excitement_data = self._analyze_window(cap, start_time, end_time, fps)
                
                if excitement_data and excitement_data['excitement_score'] > 0.4:
                    exciting_moments.append(excitement_data)
            
            return exciting_moments
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الفيديو: {e}")
            return []

    def _analyze_window(self, cap: cv2.VideoCapture, start_time: float, end_time: float, fps: float) -> Optional[Dict[str, Any]]:
        """تحليل نافذة زمنية للبحث عن الإثارة"""
        try:
            # الانتقال لبداية النافذة
            start_frame = int(start_time * fps)
            end_frame = int(end_time * fps)
            
            cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
            
            # جمع الإطارات
            frames = []
            motion_values = []
            brightness_values = []
            
            prev_gray = None
            
            for frame_idx in range(start_frame, min(end_frame, int(cap.get(cv2.CAP_PROP_FRAME_COUNT)))):
                ret, frame = cap.read()
                if not ret:
                    break
                
                # تصغير الإطار لتسريع المعالجة
                small_frame = cv2.resize(frame, (320, 240))
                gray = cv2.cvtColor(small_frame, cv2.COLOR_BGR2GRAY)
                
                # حساب السطوع
                brightness = np.mean(gray) / 255.0
                brightness_values.append(brightness)
                
                # حساب الحركة
                if prev_gray is not None:
                    diff = cv2.absdiff(gray, prev_gray)
                    motion = np.mean(diff)
                    motion_values.append(motion)
                
                frames.append(small_frame)
                prev_gray = gray.copy()
            
            if not frames or not motion_values:
                return None
            
            # تحليل الإثارة
            excitement_score = self._calculate_excitement_score(motion_values, brightness_values, frames)
            
            if excitement_score < 0.3:  # تجاهل اللحظات المملة
                return None
            
            # تحديد نوع اللحظة
            highlight_type, reasons = self._determine_highlight_type(motion_values, brightness_values, frames)
            
            # وصف اللحظة
            description = self._generate_description(highlight_type, excitement_score, reasons)
            
            return {
                'start_time': start_time,
                'end_time': end_time,
                'peak_time': (start_time + end_time) / 2,
                'excitement_score': excitement_score,
                'highlight_type': highlight_type,
                'description': description,
                'reasons': reasons,
                'motion_avg': statistics.mean(motion_values),
                'brightness_avg': statistics.mean(brightness_values),
                'motion_variance': statistics.stdev(motion_values) if len(motion_values) > 1 else 0
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل النافذة: {e}")
            return None

    def _calculate_excitement_score(self, motion_values: List[float], brightness_values: List[float], frames: List[np.ndarray]) -> float:
        """حساب نقاط الإثارة"""
        try:
            excitement = 0.0
            
            # نقاط الحركة (40%)
            if motion_values:
                avg_motion = statistics.mean(motion_values)
                motion_variance = statistics.stdev(motion_values) if len(motion_values) > 1 else 0
                
                # حركة عالية = إثارة
                motion_score = min(avg_motion / 50.0, 1.0)
                
                # تباين في الحركة = إثارة (تغييرات سريعة)
                variance_score = min(motion_variance / 20.0, 1.0)
                
                excitement += (motion_score * 0.3 + variance_score * 0.1)
            
            # نقاط السطوع (20%)
            if brightness_values:
                brightness_variance = statistics.stdev(brightness_values) if len(brightness_values) > 1 else 0
                
                # تغييرات في السطوع = إثارة
                brightness_score = min(brightness_variance * 5, 1.0)
                excitement += brightness_score * 0.2
            
            # نقاط التنوع البصري (25%)
            if frames:
                visual_variety = self._calculate_visual_variety(frames)
                excitement += visual_variety * 0.25
            
            # نقاط التغيير السريع (15%)
            if motion_values:
                rapid_changes = sum(1 for i in range(1, len(motion_values)) 
                                  if abs(motion_values[i] - motion_values[i-1]) > 10)
                change_score = min(rapid_changes / len(motion_values), 1.0)
                excitement += change_score * 0.15
            
            return min(excitement, 1.0)
            
        except Exception as e:
            logger.error(f"خطأ في حساب نقاط الإثارة: {e}")
            return 0.0

    def _calculate_visual_variety(self, frames: List[np.ndarray]) -> float:
        """حساب التنوع البصري"""
        try:
            if len(frames) < 2:
                return 0.0
            
            # مقارنة الإطارات للبحث عن تغييرات
            differences = []
            
            for i in range(1, len(frames)):
                # حساب الفرق بين الإطارات
                diff = cv2.absdiff(frames[i-1], frames[i])
                diff_score = np.mean(diff) / 255.0
                differences.append(diff_score)
            
            # متوسط التغييرات
            avg_difference = statistics.mean(differences) if differences else 0.0
            
            return min(avg_difference * 3, 1.0)  # تضخيم النتيجة
            
        except Exception as e:
            logger.error(f"خطأ في حساب التنوع البصري: {e}")
            return 0.0

    def _determine_highlight_type(self, motion_values: List[float], brightness_values: List[float], frames: List[np.ndarray]) -> Tuple[str, List[str]]:
        """تحديد نوع اللحظة المثيرة"""
        try:
            reasons = []
            
            # تحليل الحركة
            avg_motion = statistics.mean(motion_values) if motion_values else 0
            motion_variance = statistics.stdev(motion_values) if len(motion_values) > 1 else 0
            
            # تحليل السطوع
            avg_brightness = statistics.mean(brightness_values) if brightness_values else 0
            brightness_variance = statistics.stdev(brightness_values) if len(brightness_values) > 1 else 0
            
            # تحديد النوع بناءً على الخصائص
            if avg_motion > 40:
                if motion_variance > 15:
                    highlight_type = 'high_action'
                    reasons.append('حركة عالية ومتغيرة')
                else:
                    highlight_type = 'fast_motion'
                    reasons.append('حركة سريعة مستمرة')
            elif brightness_variance > 0.1:
                if avg_brightness > 0.7:
                    highlight_type = 'bright_moment'
                    reasons.append('لحظة مضيئة مثيرة')
                else:
                    highlight_type = 'dramatic_change'
                    reasons.append('تغيير دراماتيكي في الإضاءة')
            elif motion_variance > 10:
                highlight_type = 'surprise_moment'
                reasons.append('تغييرات مفاجئة في الحركة')
            elif len(frames) > 5:
                visual_variety = self._calculate_visual_variety(frames)
                if visual_variety > 0.3:
                    highlight_type = 'scene_change'
                    reasons.append('تغيير مشهد مثير')
                else:
                    highlight_type = 'funny_moment'
                    reasons.append('لحظة مضحكة محتملة')
            else:
                highlight_type = 'peak_moment'
                reasons.append('ذروة مثيرة')
            
            # إضافة أسباب إضافية
            if avg_motion > 30:
                reasons.append('حركة نشطة')
            if brightness_variance > 0.05:
                reasons.append('تغييرات في الإضاءة')
            if motion_variance > 8:
                reasons.append('ديناميكية عالية')
            
            return highlight_type, reasons
            
        except Exception as e:
            logger.error(f"خطأ في تحديد نوع اللحظة: {e}")
            return 'peak_moment', ['لحظة مثيرة']

    def _generate_description(self, highlight_type: str, excitement_score: float, reasons: List[str]) -> str:
        """إنشاء وصف للحظة المثيرة"""
        try:
            type_name = self.highlight_types.get(highlight_type, 'لحظة مثيرة')
            
            # تحديد مستوى الإثارة
            if excitement_score > 0.8:
                intensity = "عالية جداً"
            elif excitement_score > 0.6:
                intensity = "عالية"
            elif excitement_score > 0.4:
                intensity = "متوسطة"
            else:
                intensity = "منخفضة"
            
            description = f"{type_name} بإثارة {intensity}"
            
            if reasons:
                description += f" - {', '.join(reasons[:2])}"  # أول سببين فقط
            
            return description
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء الوصف: {e}")
            return "لحظة مثيرة"

    def _select_best_moments(self, moments: List[Dict[str, Any]], target_count: int) -> List[Dict[str, Any]]:
        """اختيار أفضل اللحظات"""
        try:
            if not moments:
                return []
            
            # ترتيب حسب نقاط الإثارة
            sorted_moments = sorted(moments, key=lambda x: x['excitement_score'], reverse=True)
            
            # اختيار أفضل اللحظات مع تجنب التداخل
            selected_moments = []
            
            for moment in sorted_moments:
                if len(selected_moments) >= target_count:
                    break
                
                # فحص التداخل مع اللحظات المختارة
                overlaps = False
                for selected in selected_moments:
                    if self._moments_overlap(moment, selected):
                        overlaps = True
                        break
                
                if not overlaps:
                    selected_moments.append(moment)
            
            # إذا لم نحصل على العدد المطلوب، أضف المزيد
            if len(selected_moments) < target_count and len(sorted_moments) > len(selected_moments):
                remaining = target_count - len(selected_moments)
                for moment in sorted_moments[len(selected_moments):]:
                    if remaining <= 0:
                        break
                    selected_moments.append(moment)
                    remaining -= 1
            
            return selected_moments
            
        except Exception as e:
            logger.error(f"خطأ في اختيار أفضل اللحظات: {e}")
            return moments[:target_count] if moments else []

    def _moments_overlap(self, moment1: Dict[str, Any], moment2: Dict[str, Any], min_gap: float = 10.0) -> bool:
        """فحص تداخل اللحظات"""
        try:
            # حساب المسافة بين اللحظات
            gap = min(
                abs(moment1['start_time'] - moment2['end_time']),
                abs(moment2['start_time'] - moment1['end_time'])
            )
            
            return gap < min_gap
            
        except Exception as e:
            logger.error(f"خطأ في فحص التداخل: {e}")
            return False

    def _optimize_clips(self, moments: List[Dict[str, Any]], total_duration: float) -> List[SimpleHighlight]:
        """تحسين المقاطع للحصول على أفضل نتيجة"""
        try:
            optimized_clips = []
            
            for moment in moments:
                # تحديد مدة المقطع المثلى
                moment_duration = moment['end_time'] - moment['start_time']
                
                if moment_duration < self.min_clip_duration:
                    # توسيع المقطع
                    center_time = moment['peak_time']
                    half_duration = self.optimal_duration / 2
                    
                    start_time = max(0, center_time - half_duration)
                    end_time = min(total_duration, center_time + half_duration)
                    
                    # تعديل إذا كان المقطع يتجاوز الحدود
                    if end_time - start_time < self.min_clip_duration:
                        if start_time == 0:
                            end_time = min(self.min_clip_duration, total_duration)
                        else:
                            start_time = max(0, total_duration - self.min_clip_duration)
                
                elif moment_duration > self.max_clip_duration:
                    # تقليص المقطع للتركيز على الذروة
                    center_time = moment['peak_time']
                    half_duration = self.optimal_duration / 2
                    
                    start_time = max(moment['start_time'], center_time - half_duration)
                    end_time = min(moment['end_time'], center_time + half_duration)
                
                else:
                    # المدة مناسبة
                    start_time = moment['start_time']
                    end_time = moment['end_time']
                
                # إنشاء اللحظة المحسنة
                optimized_clip = SimpleHighlight(
                    start_time=start_time,
                    end_time=end_time,
                    peak_time=moment['peak_time'],
                    highlight_type=moment['highlight_type'],
                    excitement_score=moment['excitement_score'],
                    description=moment['description'],
                    reasons=moment['reasons']
                )
                
                optimized_clips.append(optimized_clip)
            
            return optimized_clips
            
        except Exception as e:
            logger.error(f"خطأ في تحسين المقاطع: {e}")
            return []

# دالة مبسطة للاستخدام المباشر
def detect_exciting_clips(video_path: str, target_clips: int = 3) -> List[Dict[str, Any]]:
    """كشف المقاطع المثيرة - دالة مبسطة"""
    try:
        detector = SimpleHighlightDetector()
        highlights = detector.detect_exciting_moments(video_path, target_clips)
        
        # تحويل إلى تنسيق مبسط
        clips = []
        for highlight in highlights:
            clips.append({
                'start_time': highlight.start_time,
                'end_time': highlight.end_time,
                'duration': highlight.end_time - highlight.start_time,
                'excitement_score': highlight.excitement_score,
                'type': highlight.highlight_type,
                'description': highlight.description,
                'confidence': highlight.excitement_score,  # للتوافق مع النظام القديم
                'method': 'simple_excitement_detection'
            })
        
        return clips
        
    except Exception as e:
        logger.error(f"خطأ في كشف المقاطع المثيرة: {e}")
        return []

if __name__ == "__main__":
    # اختبار النظام
    print("🧪 اختبار كاشف اللحظات المثيرة المبسط")
    print("=" * 50)
    
    detector = SimpleHighlightDetector()
    print("✅ تم تهيئة الكاشف")
    print(f"🎯 أنواع اللحظات المدعومة: {len(detector.highlight_types)}")
    
    for key, value in detector.highlight_types.items():
        print(f"   - {key}: {value}")
    
    print(f"\n⚙️ إعدادات المقاطع:")
    print(f"   - الحد الأدنى: {detector.min_clip_duration}s")
    print(f"   - الحد الأقصى: {detector.max_clip_duration}s")
    print(f"   - المدة المثلى: {detector.optimal_duration}s")
    
    print("\n🏁 انتهى الاختبار")
