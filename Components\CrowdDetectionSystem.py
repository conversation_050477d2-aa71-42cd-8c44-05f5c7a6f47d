#!/usr/bin/env python3
"""
نظام كشف ردات الفعل الجماعية
Crowd Detection System

يكتشف ردات الفعل الجماعية مثل التصفيق والهتاف والتسجيل بالهواتف
مدمج من الأداة المتقدمة مع تحسينات للأداة الحالية
"""

import cv2
import numpy as np
import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
import json
import time
import statistics
from collections import deque, Counter

# محاولة استيراد المكتبات المتقدمة
try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
except ImportError:
    MEDIAPIPE_AVAILABLE = False
    mp = None

try:
    import librosa
    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False
    librosa = None

logger = logging.getLogger(__name__)

@dataclass
class CrowdEvent:
    """حدث جماعي"""
    start_time: float
    end_time: float
    peak_time: float
    event_type: str
    intensity: float
    confidence: float
    participant_count: int
    audio_features: Dict[str, float]
    visual_features: Dict[str, float]
    location_data: Dict[str, Any]

@dataclass
class CrowdAnalysis:
    """تحليل جماعي شامل"""
    total_events: int
    dominant_event_type: str
    crowd_energy_level: float
    participation_rate: float
    events: List[CrowdEvent]
    timeline: List[Dict[str, Any]]

class CrowdDetectionSystem:
    """نظام كشف ردات الفعل الجماعية"""
    
    def __init__(self):
        # إعداد MediaPipe للكشف المتقدم
        if MEDIAPIPE_AVAILABLE:
            self.mp_pose = mp.solutions.pose
            self.mp_hands = mp.solutions.hands
            self.mp_face_detection = mp.solutions.face_detection
            
            try:
                self.pose_detector = self.mp_pose.Pose(
                    static_image_mode=False,
                    model_complexity=1,
                    smooth_landmarks=True,
                    min_detection_confidence=0.5,
                    min_tracking_confidence=0.5
                )
                
                self.hands_detector = self.mp_hands.Hands(
                    static_image_mode=False,
                    max_num_hands=10,  # كشف حتى 10 أيدي
                    min_detection_confidence=0.5,
                    min_tracking_confidence=0.5
                )
                
                self.face_detector = self.mp_face_detection.FaceDetection(
                    model_selection=1,
                    min_detection_confidence=0.5
                )
                
                logger.info("تم تهيئة MediaPipe للكشف الجماعي")
            except Exception as e:
                logger.warning(f"فشل في تهيئة MediaPipe: {e}")
                self.pose_detector = None
                self.hands_detector = None
                self.face_detector = None
        else:
            logger.warning("MediaPipe غير متوفر - سيتم استخدام كشف أساسي")
            self.pose_detector = None
            self.hands_detector = None
            self.face_detector = None
        
        # أنواع الأحداث الجماعية
        self.crowd_event_types = {
            'applause': 'تصفيق جماعي',
            'cheering': 'هتاف وتشجيع',
            'phone_recording': 'تسجيل جماعي بالهواتف',
            'standing_ovation': 'وقوف وتصفيق',
            'wave_gesture': 'تلويح جماعي',
            'jumping': 'قفز جماعي',
            'singing_along': 'غناء جماعي',
            'booing': 'استهجان جماعي',
            'laughing': 'ضحك جماعي',
            'gasping': 'تنهد جماعي'
        }
        
        # عتبات الكشف
        self.min_participants = 3  # أقل عدد للحدث الجماعي
        self.applause_frequency_range = (200, 2000)  # Hz
        self.cheer_volume_threshold = 0.7
        self.phone_detection_threshold = 0.3
        self.synchronization_threshold = 0.6  # مستوى التزامن المطلوب
        
        # تخزين مؤقت للتحليل
        self.frame_history = deque(maxlen=30)  # آخر 30 إطار
        self.audio_history = deque(maxlen=50)  # آخر 50 عينة صوتية
        self.event_history = deque(maxlen=100)  # آخر 100 حدث
        
        # إعدادات التحليل
        self.analysis_window = 5.0  # نافذة 5 ثوانٍ للتحليل
        self.overlap = 2.5  # تداخل 2.5 ثانية
        
        logger.info("تم تهيئة نظام كشف ردات الفعل الجماعية")

    def analyze_crowd_behavior(self, video_path: str, audio_path: str = None) -> CrowdAnalysis:
        """تحليل السلوك الجماعي في الفيديو"""
        try:
            # تحليل الفيديو
            video_analysis = self._analyze_video_crowd_behavior(video_path)
            
            # تحليل الصوت
            audio_analysis = self._analyze_audio_crowd_behavior(audio_path or video_path)
            
            # دمج التحليلات
            combined_events = self._combine_crowd_analyses(video_analysis, audio_analysis)
            
            # تحليل شامل
            crowd_analysis = self._create_crowd_analysis(combined_events)
            
            logger.info(f"تم اكتشاف {len(combined_events)} حدث جماعي")
            return crowd_analysis
            
        except Exception as e:
            logger.error(f"خطأ في تحليل السلوك الجماعي: {e}")
            return self._get_empty_crowd_analysis()

    def _analyze_video_crowd_behavior(self, video_path: str) -> List[CrowdEvent]:
        """تحليل السلوك الجماعي في الفيديو"""
        try:
            events = []
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            frame_count = 0
            window_frames = int(self.analysis_window * fps)
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                current_time = frame_count / fps
                
                # تحليل كل نافذة
                if frame_count % int(self.overlap * fps) == 0:
                    window_events = self._analyze_video_window(cap, frame_count, window_frames, fps, current_time)
                    events.extend(window_events)
                
                frame_count += 1
            
            cap.release()
            return events
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الفيديو الجماعي: {e}")
            return []

    def _analyze_video_window(self, cap: cv2.VideoCapture, start_frame: int, 
                            window_frames: int, fps: float, timestamp: float) -> List[CrowdEvent]:
        """تحليل نافذة فيديو للأحداث الجماعية"""
        try:
            events = []
            
            # جمع إطارات النافذة
            frames = []
            cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
            
            for i in range(0, window_frames, 3):  # كل 3 إطارات
                ret, frame = cap.read()
                if not ret:
                    break
                frames.append(frame)
            
            if len(frames) < 5:  # نحتاج إطارات كافية للتحليل
                return events
            
            # تحليل الأحداث المختلفة
            applause_event = self._detect_applause_visual(frames, timestamp)
            if applause_event:
                events.append(applause_event)
            
            phone_event = self._detect_phone_recording_crowd(frames, timestamp)
            if phone_event:
                events.append(phone_event)
            
            standing_event = self._detect_standing_ovation(frames, timestamp)
            if standing_event:
                events.append(standing_event)
            
            wave_event = self._detect_wave_gesture(frames, timestamp)
            if wave_event:
                events.append(wave_event)
            
            jumping_event = self._detect_crowd_jumping(frames, timestamp)
            if jumping_event:
                events.append(jumping_event)
            
            return events
            
        except Exception as e:
            logger.error(f"خطأ في تحليل نافذة الفيديو: {e}")
            return []

    def _detect_applause_visual(self, frames: List[np.ndarray], timestamp: float) -> Optional[CrowdEvent]:
        """كشف التصفيق الجماعي بصرياً"""
        try:
            if not self.hands_detector:
                return self._detect_applause_basic(frames, timestamp)
            
            clapping_hands = []
            total_hands = 0
            
            for frame in frames:
                rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                results = self.hands_detector.process(rgb_frame)
                
                if results.multi_hand_landmarks:
                    frame_hands = len(results.multi_hand_landmarks)
                    total_hands += frame_hands
                    
                    # تحليل حركة الأيدي للتصفيق
                    clapping_count = 0
                    for hand_landmarks in results.multi_hand_landmarks:
                        if self._is_clapping_motion(hand_landmarks):
                            clapping_count += 1
                    
                    clapping_hands.append(clapping_count)
            
            # تحليل النتائج
            if not clapping_hands:
                return None
            
            avg_clapping = statistics.mean(clapping_hands)
            max_clapping = max(clapping_hands)
            
            # إذا كان هناك تصفيق من عدة أشخاص
            if max_clapping >= self.min_participants and avg_clapping >= 2:
                intensity = min(avg_clapping / 10, 1.0)
                confidence = min(max_clapping / 5, 1.0)
                
                return CrowdEvent(
                    start_time=timestamp,
                    end_time=timestamp + self.analysis_window,
                    peak_time=timestamp + self.analysis_window / 2,
                    event_type='applause',
                    intensity=intensity,
                    confidence=confidence,
                    participant_count=int(max_clapping),
                    audio_features={},
                    visual_features={'avg_clapping_hands': avg_clapping, 'max_hands': max_clapping},
                    location_data={}
                )
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في كشف التصفيق البصري: {e}")
            return None

    def _detect_applause_basic(self, frames: List[np.ndarray], timestamp: float) -> Optional[CrowdEvent]:
        """كشف التصفيق الأساسي بدون MediaPipe"""
        try:
            # كشف أساسي بناءً على الحركة
            motion_levels = []
            prev_frame = None
            
            for frame in frames:
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                
                if prev_frame is not None:
                    diff = cv2.absdiff(gray, prev_frame)
                    motion = np.mean(diff)
                    motion_levels.append(motion)
                
                prev_frame = gray
            
            if motion_levels:
                avg_motion = statistics.mean(motion_levels)
                motion_variance = statistics.stdev(motion_levels) if len(motion_levels) > 1 else 0
                
                # تصفيق = حركة عالية مع تباين (إيقاع)
                if avg_motion > 30 and motion_variance > 10:
                    return CrowdEvent(
                        start_time=timestamp,
                        end_time=timestamp + self.analysis_window,
                        peak_time=timestamp + self.analysis_window / 2,
                        event_type='applause',
                        intensity=min(avg_motion / 50, 1.0),
                        confidence=0.6,  # ثقة أقل للكشف الأساسي
                        participant_count=5,  # تقدير
                        audio_features={},
                        visual_features={'avg_motion': avg_motion, 'motion_variance': motion_variance},
                        location_data={}
                    )
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في كشف التصفيق الأساسي: {e}")
            return None

    def _detect_phone_recording_crowd(self, frames: List[np.ndarray], timestamp: float) -> Optional[CrowdEvent]:
        """كشف التسجيل الجماعي بالهواتف"""
        try:
            phone_detections = []
            
            for frame in frames:
                # كشف الأشكال المستطيلة الصغيرة (هواتف)
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                edges = cv2.Canny(gray, 50, 150)
                contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                phone_count = 0
                for contour in contours:
                    # تقريب الشكل
                    epsilon = 0.02 * cv2.arcLength(contour, True)
                    approx = cv2.approxPolyDP(contour, epsilon, True)
                    
                    # إذا كان مستطيل
                    if len(approx) == 4:
                        x, y, w, h = cv2.boundingRect(approx)
                        aspect_ratio = w / h
                        
                        # نسبة العرض للارتفاع مناسبة للهاتف
                        if 0.4 < aspect_ratio < 0.8 and 20 < w < 100 and 30 < h < 150:
                            # فحص إضافي: الهاتف يجب أن يكون مرفوع
                            if y < frame.shape[0] * 0.7:  # في الجزء العلوي من الإطار
                                phone_count += 1
                
                phone_detections.append(phone_count)
            
            if phone_detections:
                avg_phones = statistics.mean(phone_detections)
                max_phones = max(phone_detections)
                
                # إذا كان هناك عدة هواتف
                if max_phones >= self.min_participants:
                    return CrowdEvent(
                        start_time=timestamp,
                        end_time=timestamp + self.analysis_window,
                        peak_time=timestamp + self.analysis_window / 2,
                        event_type='phone_recording',
                        intensity=min(avg_phones / 10, 1.0),
                        confidence=min(max_phones / 8, 1.0),
                        participant_count=int(max_phones),
                        audio_features={},
                        visual_features={'avg_phones': avg_phones, 'max_phones': max_phones},
                        location_data={}
                    )
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في كشف التسجيل بالهواتف: {e}")
            return None

    def _detect_standing_ovation(self, frames: List[np.ndarray], timestamp: float) -> Optional[CrowdEvent]:
        """كشف الوقوف والتصفيق"""
        try:
            if not self.pose_detector:
                return None
            
            standing_people = []
            
            for frame in frames:
                rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                results = self.pose_detector.process(rgb_frame)
                
                standing_count = 0
                if results.pose_landmarks:
                    # تحليل وضعية الجسم
                    landmarks = results.pose_landmarks.landmark
                    
                    # فحص ما إذا كان الشخص واقفاً
                    if self._is_standing_pose(landmarks):
                        standing_count += 1
                
                standing_people.append(standing_count)
            
            if standing_people:
                avg_standing = statistics.mean(standing_people)
                max_standing = max(standing_people)
                
                # إذا كان هناك عدة أشخاص واقفين
                if max_standing >= self.min_participants:
                    return CrowdEvent(
                        start_time=timestamp,
                        end_time=timestamp + self.analysis_window,
                        peak_time=timestamp + self.analysis_window / 2,
                        event_type='standing_ovation',
                        intensity=min(avg_standing / 8, 1.0),
                        confidence=min(max_standing / 6, 1.0),
                        participant_count=int(max_standing),
                        audio_features={},
                        visual_features={'avg_standing': avg_standing, 'max_standing': max_standing},
                        location_data={}
                    )
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في كشف الوقوف والتصفيق: {e}")
            return None

    def _detect_wave_gesture(self, frames: List[np.ndarray], timestamp: float) -> Optional[CrowdEvent]:
        """كشف التلويح الجماعي"""
        try:
            if not self.hands_detector:
                return None

            waving_hands = []

            for frame in frames:
                rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                results = self.hands_detector.process(rgb_frame)

                wave_count = 0
                if results.multi_hand_landmarks:
                    for hand_landmarks in results.multi_hand_landmarks:
                        if self._is_waving_motion(hand_landmarks):
                            wave_count += 1

                waving_hands.append(wave_count)

            if waving_hands:
                avg_waving = statistics.mean(waving_hands)
                max_waving = max(waving_hands)

                if max_waving >= self.min_participants:
                    return CrowdEvent(
                        start_time=timestamp,
                        end_time=timestamp + self.analysis_window,
                        peak_time=timestamp + self.analysis_window / 2,
                        event_type='wave_gesture',
                        intensity=min(avg_waving / 8, 1.0),
                        confidence=min(max_waving / 6, 1.0),
                        participant_count=int(max_waving),
                        audio_features={},
                        visual_features={'avg_waving': avg_waving, 'max_waving': max_waving},
                        location_data={}
                    )

            return None

        except Exception as e:
            logger.error(f"خطأ في كشف التلويح: {e}")
            return None

    def _detect_crowd_jumping(self, frames: List[np.ndarray], timestamp: float) -> Optional[CrowdEvent]:
        """كشف القفز الجماعي"""
        try:
            if not self.pose_detector:
                return self._detect_jumping_basic(frames, timestamp)

            jumping_people = []

            for frame in frames:
                rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                results = self.pose_detector.process(rgb_frame)

                jump_count = 0
                if results.pose_landmarks:
                    landmarks = results.pose_landmarks.landmark
                    if self._is_jumping_pose(landmarks):
                        jump_count += 1

                jumping_people.append(jump_count)

            if jumping_people:
                avg_jumping = statistics.mean(jumping_people)
                max_jumping = max(jumping_people)

                if max_jumping >= self.min_participants:
                    return CrowdEvent(
                        start_time=timestamp,
                        end_time=timestamp + self.analysis_window,
                        peak_time=timestamp + self.analysis_window / 2,
                        event_type='jumping',
                        intensity=min(avg_jumping / 6, 1.0),
                        confidence=min(max_jumping / 5, 1.0),
                        participant_count=int(max_jumping),
                        audio_features={},
                        visual_features={'avg_jumping': avg_jumping, 'max_jumping': max_jumping},
                        location_data={}
                    )

            return None

        except Exception as e:
            logger.error(f"خطأ في كشف القفز: {e}")
            return None

    def _detect_jumping_basic(self, frames: List[np.ndarray], timestamp: float) -> Optional[CrowdEvent]:
        """كشف القفز الأساسي"""
        try:
            # كشف بناءً على الحركة العمودية
            vertical_motions = []
            prev_frame = None

            for frame in frames:
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

                if prev_frame is not None:
                    # حساب الحركة العمودية
                    diff = cv2.absdiff(gray, prev_frame)

                    # تقسيم الإطار إلى أجزاء عمودية
                    h, w = diff.shape
                    upper_motion = np.mean(diff[:h//2, :])  # الجزء العلوي
                    lower_motion = np.mean(diff[h//2:, :])  # الجزء السفلي

                    # القفز = حركة أكثر في الأعلى
                    vertical_ratio = upper_motion / max(lower_motion, 1)
                    vertical_motions.append(vertical_ratio)

                prev_frame = gray

            if vertical_motions:
                avg_vertical = statistics.mean(vertical_motions)
                max_vertical = max(vertical_motions)

                # إذا كانت الحركة العمودية عالية
                if max_vertical > 1.5 and avg_vertical > 1.2:
                    return CrowdEvent(
                        start_time=timestamp,
                        end_time=timestamp + self.analysis_window,
                        peak_time=timestamp + self.analysis_window / 2,
                        event_type='jumping',
                        intensity=min(avg_vertical / 3, 1.0),
                        confidence=0.6,
                        participant_count=4,  # تقدير
                        audio_features={},
                        visual_features={'avg_vertical_motion': avg_vertical, 'max_vertical_motion': max_vertical},
                        location_data={}
                    )

            return None

        except Exception as e:
            logger.error(f"خطأ في كشف القفز الأساسي: {e}")
            return None

    def _analyze_audio_crowd_behavior(self, audio_path: str) -> List[CrowdEvent]:
        """تحليل السلوك الجماعي في الصوت"""
        try:
            if not LIBROSA_AVAILABLE:
                return self._analyze_audio_basic(audio_path)

            events = []

            # تحميل الملف الصوتي
            y, sr = librosa.load(audio_path, sr=22050)
            duration = len(y) / sr

            # تحليل النوافذ الزمنية
            window_samples = int(self.analysis_window * sr)
            overlap_samples = int(self.overlap * sr)

            for start_sample in range(0, len(y) - window_samples, overlap_samples):
                end_sample = start_sample + window_samples
                window_audio = y[start_sample:end_sample]
                timestamp = start_sample / sr

                # تحليل الأحداث الصوتية
                applause_event = self._detect_applause_audio(window_audio, sr, timestamp)
                if applause_event:
                    events.append(applause_event)

                cheer_event = self._detect_cheering_audio(window_audio, sr, timestamp)
                if cheer_event:
                    events.append(cheer_event)

                laugh_event = self._detect_crowd_laughter(window_audio, sr, timestamp)
                if laugh_event:
                    events.append(laugh_event)

                boo_event = self._detect_booing_audio(window_audio, sr, timestamp)
                if boo_event:
                    events.append(boo_event)

            return events

        except Exception as e:
            logger.error(f"خطأ في تحليل الصوت الجماعي: {e}")
            return []

    def _detect_applause_audio(self, audio: np.ndarray, sr: int, timestamp: float) -> Optional[CrowdEvent]:
        """كشف التصفيق من الصوت"""
        try:
            # تحليل طيفي للتصفيق
            stft = librosa.stft(audio)
            magnitude = np.abs(stft)

            # التصفيق له ترددات عالية ومتكررة
            freqs = librosa.fft_frequencies(sr=sr)
            applause_freq_mask = (freqs >= self.applause_frequency_range[0]) & (freqs <= self.applause_frequency_range[1])

            applause_energy = np.mean(magnitude[applause_freq_mask, :])
            total_energy = np.mean(magnitude)

            # نسبة طاقة التصفيق
            applause_ratio = applause_energy / max(total_energy, 1e-10)

            # كشف الإيقاع (التصفيق له إيقاع)
            tempo, beats = librosa.beat.beat_track(y=audio, sr=sr)
            rhythm_strength = len(beats) / max(len(audio) / sr, 1)  # عدد النبضات في الثانية

            # إذا كانت النسبة عالية والإيقاع قوي
            if applause_ratio > 0.3 and rhythm_strength > 2:
                intensity = min(applause_ratio * 2, 1.0)
                confidence = min((applause_ratio + rhythm_strength / 5) / 2, 1.0)

                return CrowdEvent(
                    start_time=timestamp,
                    end_time=timestamp + self.analysis_window,
                    peak_time=timestamp + self.analysis_window / 2,
                    event_type='applause',
                    intensity=intensity,
                    confidence=confidence,
                    participant_count=int(intensity * 10),  # تقدير بناءً على الشدة
                    audio_features={
                        'applause_ratio': applause_ratio,
                        'rhythm_strength': rhythm_strength,
                        'tempo': tempo
                    },
                    visual_features={},
                    location_data={}
                )

            return None

        except Exception as e:
            logger.error(f"خطأ في كشف التصفيق الصوتي: {e}")
            return None

    def _detect_cheering_audio(self, audio: np.ndarray, sr: int, timestamp: float) -> Optional[CrowdEvent]:
        """كشف الهتاف من الصوت"""
        try:
            # الهتاف = صوت عالي مع ترددات متوسطة
            rms = librosa.feature.rms(y=audio)[0]
            avg_volume = np.mean(rms)

            # تحليل الطيف
            spectral_centroid = librosa.feature.spectral_centroid(y=audio, sr=sr)[0]
            avg_centroid = np.mean(spectral_centroid)

            # الهتاف له ترددات في المدى المتوسط (500-2000 Hz)
            if avg_volume > self.cheer_volume_threshold and 500 < avg_centroid < 2000:
                # فحص التباين (الهتاف متغير)
                volume_variance = np.var(rms)

                if volume_variance > 0.01:  # تباين كافي
                    intensity = min(avg_volume * 1.5, 1.0)
                    confidence = min((avg_volume + volume_variance * 10) / 2, 1.0)

                    return CrowdEvent(
                        start_time=timestamp,
                        end_time=timestamp + self.analysis_window,
                        peak_time=timestamp + self.analysis_window / 2,
                        event_type='cheering',
                        intensity=intensity,
                        confidence=confidence,
                        participant_count=int(intensity * 8),
                        audio_features={
                            'avg_volume': avg_volume,
                            'spectral_centroid': avg_centroid,
                            'volume_variance': volume_variance
                        },
                        visual_features={},
                        location_data={}
                    )

            return None

        except Exception as e:
            logger.error(f"خطأ في كشف الهتاف: {e}")
            return None

    def _detect_crowd_laughter(self, audio: np.ndarray, sr: int, timestamp: float) -> Optional[CrowdEvent]:
        """كشف الضحك الجماعي"""
        try:
            # الضحك له خصائص طيفية مميزة
            mfccs = librosa.feature.mfcc(y=audio, sr=sr, n_mfcc=13)

            # الضحك له أنماط MFCC مميزة
            mfcc_mean = np.mean(mfccs, axis=1)
            mfcc_var = np.var(mfccs, axis=1)

            # خصائص الضحك: تباين عالي في MFCC الأولى
            laugh_indicator = mfcc_var[1] + mfcc_var[2]  # MFCC 1 و 2

            # فحص الإيقاع (الضحك له إيقاع متقطع)
            onset_frames = librosa.onset.onset_detect(y=audio, sr=sr)
            onset_rate = len(onset_frames) / max(len(audio) / sr, 1)

            if laugh_indicator > 2 and onset_rate > 3:
                intensity = min(laugh_indicator / 5, 1.0)
                confidence = min((laugh_indicator / 5 + onset_rate / 10) / 2, 1.0)

                return CrowdEvent(
                    start_time=timestamp,
                    end_time=timestamp + self.analysis_window,
                    peak_time=timestamp + self.analysis_window / 2,
                    event_type='laughing',
                    intensity=intensity,
                    confidence=confidence,
                    participant_count=int(intensity * 6),
                    audio_features={
                        'laugh_indicator': laugh_indicator,
                        'onset_rate': onset_rate,
                        'mfcc_variance': np.mean(mfcc_var)
                    },
                    visual_features={},
                    location_data={}
                )

            return None

        except Exception as e:
            logger.error(f"خطأ في كشف الضحك: {e}")
            return None

    def _detect_booing_audio(self, audio: np.ndarray, sr: int, timestamp: float) -> Optional[CrowdEvent]:
        """كشف الاستهجان من الصوت"""
        try:
            # الاستهجان له ترددات منخفضة ومستمرة
            spectral_centroid = librosa.feature.spectral_centroid(y=audio, sr=sr)[0]
            avg_centroid = np.mean(spectral_centroid)

            # طاقة الترددات المنخفضة
            stft = librosa.stft(audio)
            magnitude = np.abs(stft)
            freqs = librosa.fft_frequencies(sr=sr)

            low_freq_mask = freqs < 500  # ترددات منخفضة
            low_freq_energy = np.mean(magnitude[low_freq_mask, :])
            total_energy = np.mean(magnitude)

            low_freq_ratio = low_freq_energy / max(total_energy, 1e-10)

            # الاستهجان = ترددات منخفضة + استمرارية
            rms = librosa.feature.rms(y=audio)[0]
            volume_stability = 1.0 - (np.std(rms) / max(np.mean(rms), 1e-10))

            if avg_centroid < 800 and low_freq_ratio > 0.4 and volume_stability > 0.6:
                intensity = min(low_freq_ratio * 2, 1.0)
                confidence = min((low_freq_ratio + volume_stability) / 2, 1.0)

                return CrowdEvent(
                    start_time=timestamp,
                    end_time=timestamp + self.analysis_window,
                    peak_time=timestamp + self.analysis_window / 2,
                    event_type='booing',
                    intensity=intensity,
                    confidence=confidence,
                    participant_count=int(intensity * 7),
                    audio_features={
                        'low_freq_ratio': low_freq_ratio,
                        'volume_stability': volume_stability,
                        'spectral_centroid': avg_centroid
                    },
                    visual_features={},
                    location_data={}
                )

            return None

        except Exception as e:
            logger.error(f"خطأ في كشف الاستهجان: {e}")
            return None

    def _analyze_audio_basic(self, audio_path: str) -> List[CrowdEvent]:
        """تحليل صوتي أساسي بدون librosa"""
        try:
            # تحليل أساسي جداً - يمكن تحسينه لاحقاً
            return []
        except Exception as e:
            logger.error(f"خطأ في التحليل الصوتي الأساسي: {e}")
            return []

    def _combine_crowd_analyses(self, video_events: List[CrowdEvent],
                              audio_events: List[CrowdEvent]) -> List[CrowdEvent]:
        """دمج تحليلات الفيديو والصوت"""
        try:
            combined_events = []

            # دمج الأحداث المتزامنة
            for video_event in video_events:
                # البحث عن أحداث صوتية متزامنة
                matching_audio = None
                for audio_event in audio_events:
                    # فحص التداخل الزمني
                    overlap = self._calculate_time_overlap(video_event, audio_event)
                    if overlap > 0.5:  # تداخل 50% على الأقل
                        matching_audio = audio_event
                        break

                if matching_audio:
                    # دمج الحدثين
                    merged_event = self._merge_events(video_event, matching_audio)
                    combined_events.append(merged_event)
                    audio_events.remove(matching_audio)  # إزالة لتجنب التكرار
                else:
                    combined_events.append(video_event)

            # إضافة الأحداث الصوتية المتبقية
            combined_events.extend(audio_events)

            # ترتيب حسب الوقت
            combined_events.sort(key=lambda x: x.start_time)

            return combined_events

        except Exception as e:
            logger.error(f"خطأ في دمج التحليلات: {e}")
            return video_events + audio_events

    def _calculate_time_overlap(self, event1: CrowdEvent, event2: CrowdEvent) -> float:
        """حساب نسبة التداخل الزمني بين حدثين"""
        try:
            start_overlap = max(event1.start_time, event2.start_time)
            end_overlap = min(event1.end_time, event2.end_time)

            if end_overlap <= start_overlap:
                return 0.0

            overlap_duration = end_overlap - start_overlap
            total_duration = min(event1.end_time - event1.start_time, event2.end_time - event2.start_time)

            return overlap_duration / max(total_duration, 1e-10)

        except Exception as e:
            logger.error(f"خطأ في حساب التداخل الزمني: {e}")
            return 0.0

    def _merge_events(self, video_event: CrowdEvent, audio_event: CrowdEvent) -> CrowdEvent:
        """دمج حدثين متزامنين"""
        try:
            # اختيار النوع الأكثر ثقة
            if video_event.confidence >= audio_event.confidence:
                primary_event = video_event
                secondary_event = audio_event
            else:
                primary_event = audio_event
                secondary_event = video_event

            # دمج الخصائص
            merged_audio_features = {**primary_event.audio_features, **secondary_event.audio_features}
            merged_visual_features = {**primary_event.visual_features, **secondary_event.visual_features}

            return CrowdEvent(
                start_time=min(video_event.start_time, audio_event.start_time),
                end_time=max(video_event.end_time, audio_event.end_time),
                peak_time=(video_event.peak_time + audio_event.peak_time) / 2,
                event_type=primary_event.event_type,
                intensity=max(video_event.intensity, audio_event.intensity),
                confidence=(video_event.confidence + audio_event.confidence) / 2,
                participant_count=max(video_event.participant_count, audio_event.participant_count),
                audio_features=merged_audio_features,
                visual_features=merged_visual_features,
                location_data=primary_event.location_data
            )

        except Exception as e:
            logger.error(f"خطأ في دمج الأحداث: {e}")
            return video_event

    def _create_crowd_analysis(self, events: List[CrowdEvent]) -> CrowdAnalysis:
        """إنشاء تحليل جماعي شامل"""
        try:
            if not events:
                return self._get_empty_crowd_analysis()

            # إحصائيات عامة
            total_events = len(events)

            # النوع المهيمن
            event_types = [event.event_type for event in events]
            dominant_type = Counter(event_types).most_common(1)[0][0] if event_types else 'none'

            # مستوى الطاقة
            avg_intensity = statistics.mean([event.intensity for event in events])

            # معدل المشاركة
            avg_participants = statistics.mean([event.participant_count for event in events])
            max_participants = max([event.participant_count for event in events])
            participation_rate = avg_participants / max(max_participants, 1)

            # الجدول الزمني
            timeline = []
            for event in events:
                timeline.append({
                    'time': event.peak_time,
                    'type': event.event_type,
                    'intensity': event.intensity,
                    'participants': event.participant_count
                })

            return CrowdAnalysis(
                total_events=total_events,
                dominant_event_type=dominant_type,
                crowd_energy_level=avg_intensity,
                participation_rate=participation_rate,
                events=events,
                timeline=timeline
            )

        except Exception as e:
            logger.error(f"خطأ في إنشاء التحليل الجماعي: {e}")
            return self._get_empty_crowd_analysis()

    def _get_empty_crowd_analysis(self) -> CrowdAnalysis:
        """إرجاع تحليل جماعي فارغ"""
        return CrowdAnalysis(
            total_events=0,
            dominant_event_type='none',
            crowd_energy_level=0.0,
            participation_rate=0.0,
            events=[],
            timeline=[]
        )

    # دوال مساعدة للكشف
    def _is_clapping_motion(self, hand_landmarks) -> bool:
        """فحص حركة التصفيق"""
        try:
            # فحص بسيط: المسافة بين راحتي اليدين
            # هذا يتطلب يدين، لكن يمكن تبسيطه لحركة يد واحدة
            wrist = hand_landmarks.landmark[0]
            middle_finger_tip = hand_landmarks.landmark[12]

            # إذا كانت الأصابع مغلقة نسبياً = تصفيق محتمل
            distance = ((wrist.x - middle_finger_tip.x)**2 + (wrist.y - middle_finger_tip.y)**2)**0.5
            return distance < 0.15

        except:
            return False

    def _is_waving_motion(self, hand_landmarks) -> bool:
        """فحص حركة التلويح"""
        try:
            # فحص ما إذا كانت اليد مرفوعة والأصابع مفتوحة
            wrist = hand_landmarks.landmark[0]
            index_tip = hand_landmarks.landmark[8]
            middle_tip = hand_landmarks.landmark[12]

            # اليد مرفوعة
            hand_raised = index_tip.y < wrist.y

            # الأصابع مفتوحة
            fingers_spread = abs(index_tip.x - middle_tip.x) > 0.05

            return hand_raised and fingers_spread

        except:
            return False

    def _is_standing_pose(self, landmarks) -> bool:
        """فحص وضعية الوقوف"""
        try:
            # فحص ما إذا كان الشخص واقفاً
            left_hip = landmarks[23]
            right_hip = landmarks[24]
            left_knee = landmarks[25]
            right_knee = landmarks[26]

            # الوركين أعلى من الركبتين = واقف
            hip_y = (left_hip.y + right_hip.y) / 2
            knee_y = (left_knee.y + right_knee.y) / 2

            return hip_y < knee_y

        except:
            return False

    def _is_jumping_pose(self, landmarks) -> bool:
        """فحص وضعية القفز"""
        try:
            # فحص ما إذا كان الشخص يقفز
            left_ankle = landmarks[27]
            right_ankle = landmarks[28]
            left_hip = landmarks[23]
            right_hip = landmarks[24]

            # القدمين أعلى من المعتاد = قفز
            ankle_y = (left_ankle.y + right_ankle.y) / 2
            hip_y = (left_hip.y + right_hip.y) / 2

            # نسبة القدم للورك أقل من المعتاد
            return (ankle_y - hip_y) < 0.3

        except:
            return False

# دوال مساعدة للتوافق مع الأداة الحالية
def detect_crowd_events(video_path: str, audio_path: str = None) -> List[Dict[str, Any]]:
    """كشف الأحداث الجماعية"""
    try:
        detector = CrowdDetectionSystem()
        analysis = detector.analyze_crowd_behavior(video_path, audio_path)
        
        # تحويل إلى تنسيق مبسط
        simple_events = []
        for event in analysis.events:
            simple_events.append({
                'start_time': event.start_time,
                'end_time': event.end_time,
                'type': event.event_type,
                'intensity': event.intensity,
                'confidence': event.confidence,
                'participants': event.participant_count
            })
        
        return simple_events
        
    except Exception as e:
        logger.error(f"خطأ في كشف الأحداث الجماعية: {e}")
        return []

if __name__ == "__main__":
    # اختبار النظام
    print("🧪 اختبار نظام كشف ردات الفعل الجماعية")
    print("=" * 60)
    
    detector = CrowdDetectionSystem()
    print(f"✅ تم تهيئة النظام")
    print(f"📊 MediaPipe متوفر: {'نعم' if MEDIAPIPE_AVAILABLE else 'لا'}")
    print(f"🔊 Librosa متوفر: {'نعم' if LIBROSA_AVAILABLE else 'لا'}")
    print(f"🎭 أنواع الأحداث الجماعية: {len(detector.crowd_event_types)}")
    
    # عرض أنواع الأحداث
    print("\n🎪 أنواع الأحداث الجماعية المدعومة:")
    for key, value in detector.crowd_event_types.items():
        print(f"   - {key}: {value}")
    
    print(f"\n⚙️ إعدادات الكشف:")
    print(f"   - أقل عدد مشاركين: {detector.min_participants}")
    print(f"   - نافذة التحليل: {detector.analysis_window}s")
    print(f"   - عتبة التزامن: {detector.synchronization_threshold}")
    
    print("\n🏁 انتهى الاختبار")
