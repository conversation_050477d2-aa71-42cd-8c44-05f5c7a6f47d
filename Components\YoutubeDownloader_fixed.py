import os
import yt_dlp

def download_youtube_video(url):
    try:
        if not os.path.exists('videos'):
            os.makedirs('videos')
        
        # Configure yt-dlp options
        ydl_opts = {
            'format': 'best[ext=mp4]',  # Download best quality mp4
            'outtmpl': 'videos/%(title)s.%(ext)s',  # Output template
            'noplaylist': True,  # Don't download playlists
        }
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            # Get video info first
            info = ydl.extract_info(url, download=False)
            title = info.get('title', 'video')
            print(f"Video title: {title}")
            
            # Download the video
            print("Downloading video...")
            ydl.download([url])
            
            # Return the path to the downloaded file
            output_file = os.path.join('videos', f"{title}.mp4")
            if os.path.exists(output_file):
                print(f"Downloaded: {title} to 'videos' folder")
                print(f"File path: {output_file}")
                return output_file
            else:
                # Try to find the file with a different name
                for file in os.listdir('videos'):
                    if file.endswith('.mp4'):
                        output_file = os.path.join('videos', file)
                        print(f"Downloaded: {file} to 'videos' folder")
                        print(f"File path: {output_file}")
                        return output_file
                        
    except Exception as e:
        print(f"An error occurred: {str(e)}")
        return None

if __name__ == "__main__":
    youtube_url = input("Enter YouTube video URL: ")
    download_youtube_video(youtube_url)
