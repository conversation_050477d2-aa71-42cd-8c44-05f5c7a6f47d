<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎬 AI Youtube Shorts Generator</title>
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎬</text></svg>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1200px;
        }
        .header {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }
        .status-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #28a745;
        }
        .video-input {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .progress-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .btn-custom {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        .video-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .highlight-item {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
        .nav-tabs .nav-link {
            border-radius: 15px 15px 0 0;
            margin-left: 5px;
        }
        .nav-tabs .nav-link.active {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="header">
                <h1><i class="fas fa-video"></i> AI Youtube Shorts Generator</h1>
                <p class="mb-0">مولد المقاطع القصيرة بالذكاء الاصطناعي</p>
            </div>

            <!-- Status Card -->
            <div class="container">
                <div class="status-card" id="statusCard">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle text-success me-3 fs-4"></i>
                        <div>
                            <h5 class="mb-1">حالة النظام</h5>
                            <p class="mb-0" id="statusMessage">جاري فحص النظام...</p>
                        </div>
                    </div>
                </div>

                <!-- Navigation Tabs -->
                <ul class="nav nav-tabs" id="mainTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="home-tab" data-bs-toggle="tab" data-bs-target="#home" type="button">
                            <i class="fas fa-home"></i> الرئيسية
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="process-tab" data-bs-toggle="tab" data-bs-target="#process" type="button">
                            <i class="fas fa-cogs"></i> معالجة فيديو
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="videos-tab" data-bs-toggle="tab" data-bs-target="#videos" type="button">
                            <i class="fas fa-film"></i> الفيديوهات
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="shorts-tab" data-bs-toggle="tab" data-bs-target="#shorts" type="button">
                            <i class="fas fa-scissors"></i> المقاطع المحفوظة
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button">
                            <i class="fas fa-cog"></i> الإعدادات
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="mainTabContent">
                    <!-- Home Tab -->
                    <div class="tab-pane fade show active" id="home" role="tabpanel">
                        <div class="row mt-4">
                            <div class="col-md-4">
                                <div class="feature-card">
                                    <i class="fas fa-download text-primary fs-1 mb-3"></i>
                                    <h5>تحميل الفيديوهات</h5>
                                    <p>تحميل فيديوهات YouTube تلقائياً بأعلى جودة</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="feature-card">
                                    <i class="fas fa-microphone text-success fs-1 mb-3"></i>
                                    <h5>استخراج الصوت</h5>
                                    <p>فصل الصوت عن الفيديو وتحويله إلى نص</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="feature-card">
                                    <i class="fas fa-robot text-warning fs-1 mb-3"></i>
                                    <h5>تحليل ذكي متقدم</h5>
                                    <p>استخدام Gemini 2.0 Pro لاستخراج أفضل المقاطع بذكاء فائق</p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="feature-card">
                                    <i class="fas fa-user text-info fs-1 mb-3"></i>
                                    <h5>كشف الوجوه</h5>
                                    <p>تحديد المتحدثين وتتبع الوجوه</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="feature-card">
                                    <i class="fas fa-cut text-danger fs-1 mb-3"></i>
                                    <h5>قص ذكي</h5>
                                    <p>اقتصاص عمودي مناسب للمقاطع القصيرة</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="feature-card">
                                    <i class="fas fa-magic text-purple fs-1 mb-3"></i>
                                    <h5>تأثيرات متقدمة</h5>
                                    <p>إضافة تأثيرات وانتقالات احترافية</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Process Tab -->
                    <div class="tab-pane fade" id="process" role="tabpanel">
                        <div class="video-input">
                            <h4><i class="fas fa-link"></i> إدخال رابط الفيديو</h4>
                            <div class="mb-3">
                                <label for="youtubeUrl" class="form-label">رابط YouTube</label>
                                <input type="url" class="form-control" id="youtubeUrl" 
                                       placeholder="https://www.youtube.com/watch?v=...">
                            </div>
                            <button class="btn btn-custom" onclick="processVideo()">
                                <i class="fas fa-play"></i> بدء المعالجة
                            </button>
                        </div>

                        <!-- Progress Section -->
                        <div class="progress-section" id="progressSection">
                            <h5><i class="fas fa-spinner fa-spin"></i> جاري المعالجة...</h5>
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     id="progressBar" style="width: 0%"></div>
                            </div>
                            <p id="progressMessage">بدء المعالجة...</p>
                        </div>
                    </div>

                    <!-- Videos Tab -->
                    <div class="tab-pane fade" id="videos" role="tabpanel">
                        <div class="mt-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h4><i class="fas fa-film"></i> الفيديوهات المعالجة</h4>
                                <div class="d-flex gap-2">
                                    <input type="text" class="form-control" id="searchVideos" placeholder="البحث في الفيديوهات..." style="width: 250px;">
                                    <button class="btn btn-outline-primary" onclick="searchVideos()">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div id="videosList">
                                <p class="text-muted text-center py-5">لا توجد فيديوهات معالجة بعد</p>
                            </div>
                        </div>
                    </div>

                    <!-- Shorts Tab -->
                    <div class="tab-pane fade" id="shorts" role="tabpanel">
                        <div class="mt-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h4><i class="fas fa-scissors"></i> المقاطع المحفوظة</h4>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-outline-success" onclick="exportData()">
                                        <i class="fas fa-download"></i> تصدير البيانات
                                    </button>
                                    <button class="btn btn-outline-info" onclick="loadStatistics()">
                                        <i class="fas fa-chart-bar"></i> الإحصائيات
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="checkAPIs()">
                                        <i class="fas fa-wifi"></i> فحص APIs
                                    </button>
                                </div>
                            </div>

                            <!-- معلومات مفيدة -->
                            <div class="alert alert-info" role="alert">
                                <h6><i class="fas fa-info-circle"></i> معلومات مهمة:</h6>
                                <ul class="mb-0">
                                    <li><strong>المقاطع الجاهزة:</strong> يمكن تحميلها مباشرة كملفات MP4</li>
                                    <li><strong>مكان الحفظ:</strong> مجلد <code>shorts/</code> في مجلد المشروع</li>
                                    <li><strong>التحميل:</strong> اضغط على زر "تحميل الملف" للمقاطع الجاهزة</li>
                                </ul>
                            </div>

                            <div id="shortsList">
                                <div class="text-center py-5">
                                    <i class="fas fa-scissors fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد مقاطع محفوظة بعد</h5>
                                    <p class="text-muted">قم بمعالجة فيديو أولاً، ثم اضغط "إنشاء مقطع" من المقاطع المقترحة</p>
                                    <a href="#" onclick="document.getElementById('process-tab').click()" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> معالجة فيديو جديد
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings Tab -->
                    <div class="tab-pane fade" id="settings" role="tabpanel">
                        <div class="mt-4">
                            <h4><i class="fas fa-cog"></i> إعدادات التطبيق</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body">
                                            <h5>إعدادات API</h5>
                                            <div class="mb-3">
                                                <label class="form-label">مفتاح OpenAI API</label>
                                                <input type="password" class="form-control" id="apiKey" 
                                                       placeholder="sk-...">
                                            </div>
                                            <button class="btn btn-custom" onclick="saveSettings()">
                                                حفظ الإعدادات
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body">
                                            <h5>إعدادات الجودة</h5>
                                            <div class="mb-3">
                                                <label class="form-label">جودة الإخراج</label>
                                                <select class="form-select" id="outputQuality">
                                                    <option value="high">عالية</option>
                                                    <option value="medium">متوسطة</option>
                                                    <option value="low">منخفضة</option>
                                                </select>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="autoCrop" checked>
                                                <label class="form-check-label" for="autoCrop">
                                                    اقتصاص تلقائي
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/app.js"></script>
</body>
</html>
