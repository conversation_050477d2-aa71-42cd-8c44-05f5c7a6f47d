#!/usr/bin/env python3
"""
محلل الصوت المتقدم
Advanced Audio Analysis System

يستخدم librosa وتقنيات متقدمة لتحليل الصوت وكشف اللحظات المثيرة
مدمج من الأداة المتقدمة مع تحسينات للأداة الحالية
"""

import numpy as np
import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
import json
import time
import statistics

# محاولة استيراد المكتبات المتقدمة
try:
    import librosa
    import librosa.display
    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False
    librosa = None

try:
    import scipy.signal
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    scipy = None

logger = logging.getLogger(__name__)

@dataclass
class AudioFeatures:
    """خصائص الصوت المستخرجة"""
    timestamp: float
    energy: float
    spectral_centroid: float
    spectral_rolloff: float
    zero_crossing_rate: float
    mfcc: List[float]
    chroma: List[float]
    tempo: float
    pitch: float
    loudness: float
    spectral_contrast: List[float]

@dataclass
class AudioHighlight:
    """لحظة صوتية مميزة"""
    start_time: float
    end_time: float
    peak_time: float
    highlight_type: str
    confidence: float
    features: AudioFeatures
    intensity_score: float
    emotional_impact: float

class AdvancedAudioAnalyzer:
    """محلل الصوت المتقدم"""
    
    def __init__(self):
        # إعدادات التحليل
        self.sample_rate = 22050  # معدل العينة المعياري
        self.hop_length = 512     # طول القفزة
        self.frame_length = 2048  # طول الإطار
        self.window_size = 3.0    # نافذة 3 ثوانٍ للتحليل
        self.overlap = 1.5        # تداخل 1.5 ثانية
        
        # عتبات الكشف
        self.energy_threshold = 0.4
        self.tempo_change_threshold = 20  # BPM
        self.pitch_change_threshold = 50  # Hz
        self.loudness_threshold = -20     # dB
        
        # أنواع اللحظات الصوتية
        self.audio_highlight_types = {
            'energy_peak': 'ذروة طاقة صوتية',
            'tempo_change': 'تغيير في الإيقاع',
            'pitch_peak': 'ذروة في النبرة',
            'silence_break': 'كسر الصمت',
            'applause_detection': 'كشف تصفيق',
            'music_climax': 'ذروة موسيقية',
            'voice_emphasis': 'تأكيد صوتي',
            'crowd_reaction': 'ردة فعل جماعية صوتية'
        }
        
        logger.info("تم تهيئة محلل الصوت المتقدم")
        logger.info(f"Librosa متوفر: {'نعم' if LIBROSA_AVAILABLE else 'لا'}")
        logger.info(f"SciPy متوفر: {'نعم' if SCIPY_AVAILABLE else 'لا'}")

    def analyze_audio_file(self, audio_path: str) -> Dict[str, Any]:
        """تحليل ملف صوتي شامل"""
        try:
            if not LIBROSA_AVAILABLE:
                return self._analyze_audio_basic(audio_path)
            
            # تحميل الملف الصوتي
            y, sr = librosa.load(audio_path, sr=self.sample_rate)
            duration = len(y) / sr
            
            logger.info(f"تحليل ملف صوتي: {duration:.1f}s، معدل العينة: {sr} Hz")
            
            # تحليل شامل
            global_features = self._extract_global_features(y, sr)
            windowed_features = self._extract_windowed_features(y, sr, duration)
            highlights = self._detect_audio_highlights(windowed_features)
            
            # تحليل متقدم للمشاعر الصوتية
            emotional_analysis = self._analyze_audio_emotions(y, sr, windowed_features)
            
            # كشف أنماط خاصة
            special_patterns = self._detect_special_audio_patterns(y, sr)
            
            return {
                'duration': duration,
                'sample_rate': sr,
                'global_features': global_features,
                'windowed_features': windowed_features,
                'highlights': highlights,
                'emotional_analysis': emotional_analysis,
                'special_patterns': special_patterns,
                'quality_metrics': self._calculate_audio_quality(y, sr)
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الملف الصوتي: {e}")
            return self._analyze_audio_basic(audio_path)

    def _extract_global_features(self, y: np.ndarray, sr: int) -> Dict[str, Any]:
        """استخراج الخصائص العامة للصوت"""
        try:
            features = {}
            
            # الطاقة الإجمالية
            features['total_energy'] = float(np.sum(y ** 2))
            features['rms_energy'] = float(np.sqrt(np.mean(y ** 2)))
            
            # الطيف الترددي
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
            features['avg_spectral_centroid'] = float(np.mean(spectral_centroids))
            features['spectral_centroid_std'] = float(np.std(spectral_centroids))
            
            # معدل عبور الصفر
            zcr = librosa.feature.zero_crossing_rate(y)[0]
            features['avg_zero_crossing_rate'] = float(np.mean(zcr))
            
            # الإيقاع العام
            tempo, beats = librosa.beat.beat_track(y=y, sr=sr)
            features['global_tempo'] = float(tempo)
            features['total_beats'] = len(beats)
            features['beat_consistency'] = self._calculate_beat_consistency(beats, sr)
            
            # MFCC (خصائص الصوت الأساسية)
            mfccs = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)
            features['mfcc_means'] = [float(np.mean(mfcc)) for mfcc in mfccs]
            features['mfcc_stds'] = [float(np.std(mfcc)) for mfcc in mfccs]
            
            # الكروما (النغمات الموسيقية)
            chroma = librosa.feature.chroma_stft(y=y, sr=sr)
            features['chroma_means'] = [float(np.mean(c)) for c in chroma]
            
            # التباين الطيفي
            spectral_contrast = librosa.feature.spectral_contrast(y=y, sr=sr)
            features['spectral_contrast_means'] = [float(np.mean(sc)) for sc in spectral_contrast]
            
            return features
            
        except Exception as e:
            logger.error(f"خطأ في استخراج الخصائص العامة: {e}")
            return {}

    def _extract_windowed_features(self, y: np.ndarray, sr: int, duration: float) -> List[AudioFeatures]:
        """استخراج خصائص النوافذ الزمنية"""
        try:
            features_list = []
            
            window_samples = int(self.window_size * sr)
            overlap_samples = int(self.overlap * sr)
            
            for start in range(0, len(y) - window_samples, overlap_samples):
                end = start + window_samples
                window_y = y[start:end]
                timestamp = start / sr
                
                # استخراج خصائص هذه النافذة
                window_features = self._extract_window_features(window_y, sr, timestamp)
                if window_features:
                    features_list.append(window_features)
            
            return features_list
            
        except Exception as e:
            logger.error(f"خطأ في استخراج خصائص النوافذ: {e}")
            return []

    def _extract_window_features(self, window_y: np.ndarray, sr: int, timestamp: float) -> Optional[AudioFeatures]:
        """استخراج خصائص نافذة واحدة"""
        try:
            # الطاقة
            energy = float(np.sqrt(np.mean(window_y ** 2)))
            
            # الطيف الترددي
            spectral_centroid = float(np.mean(librosa.feature.spectral_centroid(y=window_y, sr=sr)[0]))
            spectral_rolloff = float(np.mean(librosa.feature.spectral_rolloff(y=window_y, sr=sr)[0]))
            
            # معدل عبور الصفر
            zcr = float(np.mean(librosa.feature.zero_crossing_rate(window_y)[0]))
            
            # MFCC
            mfccs = librosa.feature.mfcc(y=window_y, sr=sr, n_mfcc=13)
            mfcc_means = [float(np.mean(mfcc)) for mfcc in mfccs]
            
            # الكروما
            chroma = librosa.feature.chroma_stft(y=window_y, sr=sr)
            chroma_means = [float(np.mean(c)) for c in chroma]
            
            # الإيقاع المحلي
            try:
                tempo = float(librosa.beat.tempo(y=window_y, sr=sr)[0])
            except:
                tempo = 0.0
            
            # النبرة (تقدير أساسي)
            try:
                pitches, magnitudes = librosa.piptrack(y=window_y, sr=sr)
                pitch = float(np.mean(pitches[pitches > 0])) if np.any(pitches > 0) else 0.0
            except:
                pitch = 0.0
            
            # الصوت (dB)
            loudness = float(20 * np.log10(max(energy, 1e-10)))
            
            # التباين الطيفي
            try:
                spectral_contrast = librosa.feature.spectral_contrast(y=window_y, sr=sr)
                contrast_means = [float(np.mean(sc)) for sc in spectral_contrast]
            except:
                contrast_means = [0.0] * 7
            
            return AudioFeatures(
                timestamp=timestamp,
                energy=energy,
                spectral_centroid=spectral_centroid,
                spectral_rolloff=spectral_rolloff,
                zero_crossing_rate=zcr,
                mfcc=mfcc_means,
                chroma=chroma_means,
                tempo=tempo,
                pitch=pitch,
                loudness=loudness,
                spectral_contrast=contrast_means
            )
            
        except Exception as e:
            logger.error(f"خطأ في استخراج خصائص النافذة: {e}")
            return None

    def _calculate_beat_consistency(self, beats: np.ndarray, sr: int) -> float:
        """حساب انتظام الإيقاع"""
        try:
            if len(beats) < 3:
                return 0.0
            
            # حساب الفترات بين النبضات
            beat_intervals = np.diff(beats) / sr
            
            # حساب الانحراف المعياري (كلما قل = إيقاع أكثر انتظاماً)
            consistency = 1.0 - min(np.std(beat_intervals) / np.mean(beat_intervals), 1.0)
            return float(consistency)
            
        except:
            return 0.0

    def _detect_audio_highlights(self, features_list: List[AudioFeatures]) -> List[AudioHighlight]:
        """كشف اللحظات الصوتية المميزة"""
        try:
            highlights = []
            
            if not features_list:
                return highlights
            
            # استخراج المتسلسلات الزمنية
            timestamps = [f.timestamp for f in features_list]
            energies = [f.energy for f in features_list]
            tempos = [f.tempo for f in features_list]
            pitches = [f.pitch for f in features_list]
            loudness = [f.loudness for f in features_list]
            
            # كشف ذروات الطاقة
            energy_peaks = self._find_peaks_in_series(energies, timestamps, self.energy_threshold)
            for peak_time, peak_value in energy_peaks:
                highlight = self._create_audio_highlight(
                    peak_time, peak_value, 'energy_peak', features_list
                )
                if highlight:
                    highlights.append(highlight)
            
            # كشف تغييرات الإيقاع
            tempo_changes = self._find_significant_changes(tempos, timestamps, self.tempo_change_threshold)
            for change_time, change_value in tempo_changes:
                highlight = self._create_audio_highlight(
                    change_time, change_value, 'tempo_change', features_list
                )
                if highlight:
                    highlights.append(highlight)
            
            # كشف ذروات النبرة
            pitch_peaks = self._find_peaks_in_series(pitches, timestamps, self.pitch_change_threshold)
            for peak_time, peak_value in pitch_peaks:
                highlight = self._create_audio_highlight(
                    peak_time, peak_value, 'pitch_peak', features_list
                )
                if highlight:
                    highlights.append(highlight)
            
            # كشف كسر الصمت
            silence_breaks = self._detect_silence_breaks(energies, timestamps)
            for break_time, break_value in silence_breaks:
                highlight = self._create_audio_highlight(
                    break_time, break_value, 'silence_break', features_list
                )
                if highlight:
                    highlights.append(highlight)
            
            return highlights
            
        except Exception as e:
            logger.error(f"خطأ في كشف اللحظات الصوتية: {e}")
            return []

    def _find_peaks_in_series(self, values: List[float], timestamps: List[float],
                             threshold: float) -> List[Tuple[float, float]]:
        """العثور على الذروات في متسلسلة زمنية"""
        try:
            if not SCIPY_AVAILABLE:
                return self._find_peaks_basic(values, timestamps, threshold)

            # استخدام scipy لكشف الذروات
            peaks, properties = scipy.signal.find_peaks(
                values,
                height=threshold,
                distance=int(len(values) * 0.1)  # مسافة دنيا بين الذروات
            )

            peak_times_values = []
            for peak_idx in peaks:
                if peak_idx < len(timestamps):
                    peak_times_values.append((timestamps[peak_idx], values[peak_idx]))

            return peak_times_values

        except Exception as e:
            logger.error(f"خطأ في العثور على الذروات: {e}")
            return self._find_peaks_basic(values, timestamps, threshold)

    def _find_peaks_basic(self, values: List[float], timestamps: List[float],
                         threshold: float) -> List[Tuple[float, float]]:
        """كشف ذروات أساسي بدون scipy"""
        try:
            peaks = []

            for i in range(1, len(values) - 1):
                # فحص ما إذا كانت هذه النقطة ذروة محلية
                if (values[i] > values[i-1] and values[i] > values[i+1] and
                    values[i] >= threshold):
                    peaks.append((timestamps[i], values[i]))

            return peaks

        except Exception as e:
            logger.error(f"خطأ في كشف الذروات الأساسي: {e}")
            return []

    def _find_significant_changes(self, values: List[float], timestamps: List[float],
                                threshold: float) -> List[Tuple[float, float]]:
        """العثور على تغييرات مهمة في المتسلسلة"""
        try:
            changes = []

            for i in range(1, len(values)):
                change = abs(values[i] - values[i-1])
                if change >= threshold:
                    changes.append((timestamps[i], change))

            return changes

        except Exception as e:
            logger.error(f"خطأ في العثور على التغييرات: {e}")
            return []

    def _detect_silence_breaks(self, energies: List[float], timestamps: List[float]) -> List[Tuple[float, float]]:
        """كشف كسر الصمت"""
        try:
            breaks = []
            silence_threshold = np.mean(energies) * 0.1  # 10% من متوسط الطاقة

            in_silence = False
            silence_start = 0

            for i, energy in enumerate(energies):
                if energy <= silence_threshold:
                    if not in_silence:
                        in_silence = True
                        silence_start = i
                else:
                    if in_silence:
                        # انتهى الصمت
                        silence_duration = i - silence_start
                        if silence_duration >= 3:  # صمت لمدة 3 نوافذ على الأقل
                            breaks.append((timestamps[i], energy))
                        in_silence = False

            return breaks

        except Exception as e:
            logger.error(f"خطأ في كشف كسر الصمت: {e}")
            return []

    def _create_audio_highlight(self, peak_time: float, peak_value: float,
                              highlight_type: str, features_list: List[AudioFeatures]) -> Optional[AudioHighlight]:
        """إنشاء لحظة صوتية مميزة"""
        try:
            # العثور على الخصائص المطابقة
            peak_features = None
            for features in features_list:
                if abs(features.timestamp - peak_time) < 0.5:  # ضمن نصف ثانية
                    peak_features = features
                    break

            if not peak_features:
                return None

            # تحديد حدود اللحظة
            start_time = max(0, peak_time - 1.5)  # 1.5 ثانية قبل الذروة
            end_time = peak_time + 1.5  # 1.5 ثانية بعد الذروة

            # حساب الثقة والشدة
            confidence = min(peak_value / (np.mean([f.energy for f in features_list]) * 2), 1.0)
            intensity_score = self._calculate_intensity_score(peak_features, features_list)
            emotional_impact = self._calculate_emotional_impact(peak_features, highlight_type)

            return AudioHighlight(
                start_time=start_time,
                end_time=end_time,
                peak_time=peak_time,
                highlight_type=highlight_type,
                confidence=confidence,
                features=peak_features,
                intensity_score=intensity_score,
                emotional_impact=emotional_impact
            )

        except Exception as e:
            logger.error(f"خطأ في إنشاء اللحظة الصوتية: {e}")
            return None

    def _calculate_intensity_score(self, features: AudioFeatures, all_features: List[AudioFeatures]) -> float:
        """حساب نقاط الشدة"""
        try:
            # مقارنة مع المتوسط العام
            avg_energy = np.mean([f.energy for f in all_features])
            avg_tempo = np.mean([f.tempo for f in all_features if f.tempo > 0])
            avg_pitch = np.mean([f.pitch for f in all_features if f.pitch > 0])

            # حساب النقاط النسبية
            energy_score = features.energy / max(avg_energy, 0.001)
            tempo_score = features.tempo / max(avg_tempo, 1) if avg_tempo > 0 else 1
            pitch_score = features.pitch / max(avg_pitch, 1) if avg_pitch > 0 else 1

            # دمج النقاط
            intensity = (energy_score * 0.5 + tempo_score * 0.3 + pitch_score * 0.2)
            return min(intensity, 2.0) / 2.0  # تطبيع إلى [0, 1]

        except Exception as e:
            logger.error(f"خطأ في حساب نقاط الشدة: {e}")
            return 0.5

    def _calculate_emotional_impact(self, features: AudioFeatures, highlight_type: str) -> float:
        """حساب التأثير العاطفي"""
        try:
            impact = 0.5  # قيمة افتراضية

            # تحليل بناءً على نوع اللحظة
            if highlight_type == 'energy_peak':
                impact = min(features.energy * 2, 1.0)
            elif highlight_type == 'tempo_change':
                impact = min(features.tempo / 140, 1.0)  # 140 BPM كمرجع
            elif highlight_type == 'pitch_peak':
                impact = min(features.pitch / 500, 1.0)  # 500 Hz كمرجع
            elif highlight_type == 'silence_break':
                impact = 0.8  # كسر الصمت له تأثير عالي
            elif highlight_type in ['applause_detection', 'crowd_reaction']:
                impact = 0.9  # ردود الفعل الجماعية لها تأثير عالي

            # تعديل بناءً على الخصائص الإضافية
            if features.loudness > -10:  # صوت عالي
                impact += 0.1
            if features.spectral_centroid > 2000:  # ترددات عالية
                impact += 0.1

            return min(impact, 1.0)

        except Exception as e:
            logger.error(f"خطأ في حساب التأثير العاطفي: {e}")
            return 0.5

    def _analyze_audio_emotions(self, y: np.ndarray, sr: int,
                              features_list: List[AudioFeatures]) -> Dict[str, Any]:
        """تحليل المشاعر الصوتية"""
        try:
            emotions = {
                'excitement_level': 0.0,
                'calmness_level': 0.0,
                'tension_level': 0.0,
                'joy_level': 0.0,
                'energy_progression': [],
                'emotional_peaks': []
            }

            if not features_list:
                return emotions

            # حساب مستويات المشاعر
            energies = [f.energy for f in features_list]
            tempos = [f.tempo for f in features_list if f.tempo > 0]
            pitches = [f.pitch for f in features_list if f.pitch > 0]

            # مستوى الإثارة (بناءً على الطاقة والإيقاع)
            avg_energy = np.mean(energies)
            avg_tempo = np.mean(tempos) if tempos else 0
            emotions['excitement_level'] = min((avg_energy * 2 + avg_tempo / 140) / 3, 1.0)

            # مستوى الهدوء (عكس التباين)
            energy_variance = np.var(energies)
            emotions['calmness_level'] = max(0, 1.0 - energy_variance * 10)

            # مستوى التوتر (بناءً على تغيرات النبرة)
            if pitches:
                pitch_variance = np.var(pitches)
                emotions['tension_level'] = min(pitch_variance / 10000, 1.0)

            # مستوى الفرح (بناءً على الإيقاع والطاقة المستقرة)
            if tempos and avg_tempo > 100:
                tempo_consistency = 1.0 - (np.std(tempos) / max(np.mean(tempos), 1))
                emotions['joy_level'] = min((avg_tempo / 140) * tempo_consistency, 1.0)

            # تطور الطاقة عبر الوقت
            emotions['energy_progression'] = [
                {'time': f.timestamp, 'energy': f.energy} for f in features_list
            ]

            # الذروات العاطفية
            energy_threshold = np.mean(energies) + np.std(energies)
            for features in features_list:
                if features.energy > energy_threshold:
                    emotions['emotional_peaks'].append({
                        'time': features.timestamp,
                        'intensity': features.energy,
                        'type': 'energy_peak'
                    })

            return emotions

        except Exception as e:
            logger.error(f"خطأ في تحليل المشاعر الصوتية: {e}")
            return {}

    def _detect_special_audio_patterns(self, y: np.ndarray, sr: int) -> Dict[str, Any]:
        """كشف أنماط صوتية خاصة"""
        try:
            patterns = {
                'applause_detected': False,
                'music_detected': False,
                'speech_detected': False,
                'crowd_noise_detected': False,
                'silence_periods': [],
                'dominant_frequencies': []
            }

            # كشف التصفيق (ترددات عالية متكررة)
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
            high_freq_ratio = np.mean(spectral_centroids > 3000)
            if high_freq_ratio > 0.3:
                patterns['applause_detected'] = True

            # كشف الموسيقى (انتظام في الإيقاع)
            tempo, beats = librosa.beat.beat_track(y=y, sr=sr)
            if len(beats) > 10 and tempo > 60:
                beat_consistency = self._calculate_beat_consistency(beats, sr)
                if beat_consistency > 0.7:
                    patterns['music_detected'] = True

            # كشف الكلام (MFCC patterns)
            mfccs = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)
            speech_indicators = np.mean(mfccs[1:4], axis=1)  # MFCC 1-3 مهمة للكلام
            if np.std(speech_indicators) > 0.5:
                patterns['speech_detected'] = True

            # كشف ضوضاء الجمهور (طاقة عالية مع تباين)
            rms = librosa.feature.rms(y=y)[0]
            if np.mean(rms) > 0.1 and np.std(rms) > 0.05:
                patterns['crowd_noise_detected'] = True

            # كشف فترات الصمت
            silence_threshold = np.mean(rms) * 0.1
            silence_frames = rms < silence_threshold

            # تجميع فترات الصمت المتتالية
            silence_periods = []
            in_silence = False
            silence_start = 0

            for i, is_silent in enumerate(silence_frames):
                if is_silent and not in_silence:
                    in_silence = True
                    silence_start = i
                elif not is_silent and in_silence:
                    silence_duration = (i - silence_start) * self.hop_length / sr
                    if silence_duration > 1.0:  # صمت أكثر من ثانية
                        silence_periods.append({
                            'start': silence_start * self.hop_length / sr,
                            'duration': silence_duration
                        })
                    in_silence = False

            patterns['silence_periods'] = silence_periods

            # الترددات المهيمنة
            fft = np.fft.fft(y)
            freqs = np.fft.fftfreq(len(fft), 1/sr)
            magnitude = np.abs(fft)

            # العثور على أعلى 5 ترددات
            top_freq_indices = np.argsort(magnitude)[-5:]
            patterns['dominant_frequencies'] = [
                {'frequency': float(abs(freqs[i])), 'magnitude': float(magnitude[i])}
                for i in top_freq_indices if freqs[i] > 0
            ]

            return patterns

        except Exception as e:
            logger.error(f"خطأ في كشف الأنماط الخاصة: {e}")
            return {}

    def _calculate_audio_quality(self, y: np.ndarray, sr: int) -> Dict[str, float]:
        """حساب مقاييس جودة الصوت"""
        try:
            quality = {}

            # نسبة الإشارة إلى الضوضاء (تقدير)
            signal_power = np.mean(y ** 2)
            noise_estimate = np.mean(np.abs(np.diff(y)) ** 2)  # تقدير بسيط للضوضاء
            snr = 10 * np.log10(signal_power / max(noise_estimate, 1e-10))
            quality['snr_estimate'] = float(snr)

            # مدى ديناميكي
            rms = librosa.feature.rms(y=y)[0]
            dynamic_range = 20 * np.log10(np.max(rms) / max(np.min(rms[rms > 0]), 1e-10))
            quality['dynamic_range'] = float(dynamic_range)

            # معدل القطع (clipping)
            clipping_ratio = np.mean(np.abs(y) > 0.95)
            quality['clipping_ratio'] = float(clipping_ratio)

            # انتظام الطيف
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
            spectral_consistency = 1.0 - (np.std(spectral_centroids) / max(np.mean(spectral_centroids), 1))
            quality['spectral_consistency'] = float(spectral_consistency)

            return quality

        except Exception as e:
            logger.error(f"خطأ في حساب جودة الصوت: {e}")
            return {}

    def _analyze_audio_basic(self, audio_path: str) -> Dict[str, Any]:
        """تحليل صوتي أساسي بدون librosa"""
        try:
            # تحليل أساسي جداً - يمكن تحسينه لاحقاً
            return {
                'duration': 60.0,  # افتراضي
                'sample_rate': 22050,
                'global_features': {'total_energy': 1.0, 'global_tempo': 120},
                'windowed_features': [],
                'highlights': [],
                'emotional_analysis': {'excitement_level': 0.5},
                'special_patterns': {'speech_detected': True},
                'quality_metrics': {'snr_estimate': 20.0}
            }
        except Exception as e:
            logger.error(f"خطأ في التحليل الأساسي: {e}")
            return {}

# دوال مساعدة للتوافق مع الأداة الحالية
def analyze_audio_advanced(audio_path: str) -> Dict[str, Any]:
    """دالة مبسطة لتحليل الصوت المتقدم"""
    try:
        analyzer = AdvancedAudioAnalyzer()
        return analyzer.analyze_audio_file(audio_path)
    except Exception as e:
        logger.error(f"خطأ في تحليل الصوت المتقدم: {e}")
        return {}

def extract_audio_highlights(audio_path: str) -> List[Dict[str, Any]]:
    """استخراج اللحظات الصوتية المميزة"""
    try:
        analysis = analyze_audio_advanced(audio_path)
        highlights = analysis.get('highlights', [])
        
        # تحويل إلى تنسيق مبسط
        simple_highlights = []
        for highlight in highlights:
            simple_highlights.append({
                'start_time': highlight.start_time,
                'end_time': highlight.end_time,
                'type': highlight.highlight_type,
                'confidence': highlight.confidence,
                'intensity': highlight.intensity_score
            })
        
        return simple_highlights
        
    except Exception as e:
        logger.error(f"خطأ في استخراج اللحظات الصوتية: {e}")
        return []

if __name__ == "__main__":
    # اختبار النظام
    print("🧪 اختبار محلل الصوت المتقدم")
    print("=" * 50)
    
    analyzer = AdvancedAudioAnalyzer()
    print(f"✅ تم تهيئة المحلل")
    print(f"📊 Librosa متوفر: {'نعم' if LIBROSA_AVAILABLE else 'لا'}")
    print(f"🔬 SciPy متوفر: {'نعم' if SCIPY_AVAILABLE else 'لا'}")
    print(f"🎵 أنواع اللحظات الصوتية: {len(analyzer.audio_highlight_types)}")
    
    # عرض أنواع اللحظات الصوتية
    print("\n🎼 أنواع اللحظات الصوتية المدعومة:")
    for key, value in analyzer.audio_highlight_types.items():
        print(f"   - {key}: {value}")
    
    print(f"\n⚙️ إعدادات التحليل:")
    print(f"   - معدل العينة: {analyzer.sample_rate} Hz")
    print(f"   - حجم النافذة: {analyzer.window_size}s")
    print(f"   - التداخل: {analyzer.overlap}s")
    
    print("\n🏁 انتهى الاختبار")
