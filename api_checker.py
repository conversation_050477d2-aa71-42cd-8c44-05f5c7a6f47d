#!/usr/bin/env python3
"""
فحص صحة مفاتيح API المستخدمة في النظام
API Keys Validation Module
"""

import os
import requests
import json
from dotenv import load_dotenv
from typing import Dict, Tuple, Optional

# تحميل متغيرات البيئة
load_dotenv()

class APIChecker:
    """فئة للتحقق من صحة مفاتيح API"""
    
    def __init__(self):
        self.openai_api_key = os.getenv('OPENAI_API')
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        self.results = {}
    
    def check_openai_api(self) -> Tuple[bool, str]:
        """التحقق من صحة مفتاح OpenAI API"""
        try:
            # التحقق من وجود المفتاح
            if not self.openai_api_key:
                return False, "مفتاح OpenAI API غير موجود في ملف .env"
            
            # التحقق من تنسيق المفتاح
            if not self.openai_api_key.startswith('sk-'):
                return False, "تنسيق مفتاح OpenAI API غير صحيح (يجب أن يبدأ بـ sk-)"
            
            # التحقق من أن المفتاح ليس المفتاح التجريبي
            if self.openai_api_key == 'sk-test-key-for-demo-purposes-only':
                return False, "يتم استخدام مفتاح تجريبي، يرجى وضع مفتاح OpenAI API الحقيقي"
            
            # التحقق من طول المفتاح
            if len(self.openai_api_key) < 50:
                return False, "مفتاح OpenAI API قصير جداً، تأكد من صحة المفتاح"
            
            # اختبار الاتصال بـ OpenAI API
            headers = {
                'Authorization': f'Bearer {self.openai_api_key}',
                'Content-Type': 'application/json'
            }
            
            # استخدام endpoint بسيط للتحقق
            response = requests.get(
                'https://api.openai.com/v1/models',
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                return True, "مفتاح OpenAI API صحيح ويعمل"
            elif response.status_code == 401:
                return False, "مفتاح OpenAI API غير صحيح أو منتهي الصلاحية"
            elif response.status_code == 429:
                return False, "تم تجاوز حد الاستخدام لمفتاح OpenAI API"
            else:
                return False, f"خطأ في الاتصال بـ OpenAI API: {response.status_code}"
                
        except requests.exceptions.Timeout:
            return False, "انتهت مهلة الاتصال بـ OpenAI API"
        except requests.exceptions.ConnectionError:
            return False, "فشل في الاتصال بـ OpenAI API - تحقق من الإنترنت"
        except Exception as e:
            return False, f"خطأ غير متوقع في فحص OpenAI API: {str(e)}"

    def check_gemini_api(self) -> Tuple[bool, str]:
        """التحقق من صحة مفتاح Gemini API"""
        try:
            # التحقق من وجود المفتاح
            if not self.gemini_api_key:
                return False, "مفتاح Gemini API غير موجود في ملف .env"

            # التحقق من تنسيق المفتاح
            if not self.gemini_api_key.startswith('AIza'):
                return False, "تنسيق مفتاح Gemini API غير صحيح (يجب أن يبدأ بـ AIza)"

            # التحقق من طول المفتاح
            if len(self.gemini_api_key) < 30:
                return False, "مفتاح Gemini API قصير جداً، تأكد من صحة المفتاح"

            # اختبار الاتصال بـ Gemini API
            import google.generativeai as genai

            genai.configure(api_key=self.gemini_api_key)
            model = genai.GenerativeModel('gemini-2.0-flash-exp')

            # اختبار بسيط
            response = model.generate_content("مرحبا")

            if response and response.text:
                return True, "مفتاح Gemini API صحيح ويعمل"
            else:
                return False, "لم يتم الحصول على رد من Gemini API"

        except ImportError:
            return False, "مكتبة google-generativeai غير مثبتة"
        except Exception as e:
            error_msg = str(e).lower()
            if "api_key" in error_msg or "invalid" in error_msg:
                return False, "مفتاح Gemini API غير صحيح أو منتهي الصلاحية"
            elif "quota" in error_msg or "limit" in error_msg:
                return False, "تم تجاوز حد الاستخدام لمفتاح Gemini API"
            else:
                return False, f"خطأ في الاتصال بـ Gemini API: {str(e)}"
    
    def check_youtube_api(self) -> Tuple[bool, str]:
        """التحقق من إمكانية الوصول لـ YouTube (لا يحتاج API key)"""
        try:
            # اختبار الوصول لـ YouTube
            response = requests.get('https://www.youtube.com', timeout=10)
            if response.status_code == 200:
                return True, "الوصول إلى YouTube متاح"
            else:
                return False, f"مشكلة في الوصول إلى YouTube: {response.status_code}"
        except requests.exceptions.Timeout:
            return False, "انتهت مهلة الاتصال بـ YouTube"
        except requests.exceptions.ConnectionError:
            return False, "فشل في الاتصال بـ YouTube - تحقق من الإنترنت"
        except Exception as e:
            return False, f"خطأ في فحص YouTube: {str(e)}"
    
    def check_internet_connection(self) -> Tuple[bool, str]:
        """التحقق من الاتصال بالإنترنت"""
        try:
            # اختبار الاتصال بـ Google DNS
            response = requests.get('https://8.8.8.8', timeout=5)
            return True, "الاتصال بالإنترنت متاح"
        except:
            try:
                # اختبار بديل
                response = requests.get('https://www.google.com', timeout=5)
                return True, "الاتصال بالإنترنت متاح"
            except:
                return False, "لا يوجد اتصال بالإنترنت"
    
    def check_all_apis(self) -> Dict[str, Dict[str, any]]:
        """فحص جميع APIs المستخدمة"""
        print("🔍 فحص جميع APIs...")
        
        # فحص الاتصال بالإنترنت
        internet_ok, internet_msg = self.check_internet_connection()
        self.results['internet'] = {
            'status': internet_ok,
            'message': internet_msg,
            'required': True
        }
        
        # فحص YouTube
        youtube_ok, youtube_msg = self.check_youtube_api()
        self.results['youtube'] = {
            'status': youtube_ok,
            'message': youtube_msg,
            'required': True
        }
        
        # فحص OpenAI API
        openai_ok, openai_msg = self.check_openai_api()
        self.results['openai'] = {
            'status': openai_ok,
            'message': openai_msg,
            'required': False  # اختياري للميزات المتقدمة
        }

        # فحص Gemini API
        gemini_ok, gemini_msg = self.check_gemini_api()
        self.results['gemini'] = {
            'status': gemini_ok,
            'message': gemini_msg,
            'required': False  # اختياري للميزات المتقدمة
        }
        
        return self.results
    
    def print_results(self):
        """طباعة نتائج الفحص"""
        print("\n" + "="*60)
        print("📊 نتائج فحص APIs")
        print("="*60)
        
        for api_name, result in self.results.items():
            status_icon = "✅" if result['status'] else "❌"
            required_text = "(مطلوب)" if result['required'] else "(اختياري)"
            
            print(f"{status_icon} {api_name.upper()} {required_text}: {result['message']}")
        
        print("\n" + "="*60)
        
        # تحديد الحالة العامة
        required_apis = [r for r in self.results.values() if r['required']]
        all_required_ok = all(r['status'] for r in required_apis)
        
        if all_required_ok:
            print("🎉 جميع APIs المطلوبة تعمل بشكل صحيح!")
            print("💡 يمكنك الآن استخدام الأداة بشكل طبيعي")
        else:
            print("⚠️  بعض APIs المطلوبة لا تعمل")
            print("💡 راجع الأخطاء أعلاه وقم بإصلاحها")
        
        # نصائح للمفاتيح الاختيارية
        ai_apis_available = (
            self.results.get('openai', {}).get('status', False) or
            self.results.get('gemini', {}).get('status', False)
        )

        if not ai_apis_available:
            print("\n📝 ملاحظة حول APIs الذكاء الاصطناعي:")
            print("   - الأداة ستعمل بدون AI APIs ولكن بميزات محدودة")
            print("   - للحصول على الميزات الكاملة، احصل على أحد المفاتيح التالية:")
            print("   - OpenAI API: https://platform.openai.com/api-keys")
            print("   - Gemini API: https://makersuite.google.com/app/apikey")
            print("   - ضع المفتاح في ملف .env:")
            print("     OPENAI_API=sk-your-key-here")
            print("     GEMINI_API_KEY=your-gemini-key-here")
        elif self.results.get('gemini', {}).get('status', False):
            print("\n✨ Gemini API متاح - ستحصل على ميزات الذكاء الاصطناعي المتقدمة!")
    
    def get_api_status_summary(self) -> Dict[str, bool]:
        """الحصول على ملخص حالة APIs"""
        return {
            'all_required_ok': all(r['status'] for r in self.results.values() if r['required']),
            'openai_ok': self.results.get('openai', {}).get('status', False),
            'gemini_ok': self.results.get('gemini', {}).get('status', False),
            'ai_available': (self.results.get('openai', {}).get('status', False) or
                           self.results.get('gemini', {}).get('status', False)),
            'internet_ok': self.results.get('internet', {}).get('status', False),
            'youtube_ok': self.results.get('youtube', {}).get('status', False)
        }

def main():
    """الدالة الرئيسية للاختبار"""
    print("🔍 فحص APIs - AI Youtube Shorts Generator")
    
    checker = APIChecker()
    results = checker.check_all_apis()
    checker.print_results()
    
    return checker.get_api_status_summary()

if __name__ == "__main__":
    main()
