#!/usr/bin/env python3
"""
نظام التكامل المتقدم
Advanced Integration System

يربط جميع المكونات المتقدمة في نظام موحد للتحليل والتحسين الذكي
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

# استيراد جميع المكونات المتقدمة
try:
    from .AdvancedAudioAnalyzer import AdvancedAudioAnalyzer
    from .AdvancedVisualAnalyzer import AdvancedVisualAnalyzer
    from .AdvancedTextAnalyzer import AdvancedTextAnalyzer
    from .CrowdDetectionSystem import CrowdDetectionSystem
    from .MultiAPIManager import MultiAPIManager
    from .IntelligentAnalysisEngine import IntelligentAnalysisEngine
    from .AutoOptimizationSystem import AutoOptimizationSystem
except ImportError:
    # في حالة التشغيل المباشر
    import sys
    sys.path.append('.')
    from AdvancedAudioAnalyzer import AdvancedAudioAnalyzer
    from AdvancedVisualAnalyzer import AdvancedVisualAnalyzer
    from AdvancedTextAnalyzer import AdvancedTextAnalyzer
    from CrowdDetectionSystem import CrowdDetectionSystem
    from MultiAPIManager import MultiAPIManager
    from IntelligentAnalysisEngine import IntelligentAnalysisEngine
    from AutoOptimizationSystem import AutoOptimizationSystem

logger = logging.getLogger(__name__)

class AdvancedShortsGenerator:
    """مولد الشورتس المتقدم المدمج"""
    
    def __init__(self):
        """تهيئة جميع المكونات المتقدمة"""
        try:
            # تهيئة المحللات المتخصصة
            self.audio_analyzer = AdvancedAudioAnalyzer()
            self.visual_analyzer = AdvancedVisualAnalyzer()
            self.text_analyzer = AdvancedTextAnalyzer()
            self.crowd_detector = CrowdDetectionSystem()
            self.api_manager = MultiAPIManager()
            
            # تهيئة المحرك الذكي ونظام التحسين
            self.intelligence_engine = IntelligentAnalysisEngine()
            self.optimization_system = AutoOptimizationSystem()
            
            # إعدادات النظام
            self.system_config = {
                'enable_audio_analysis': True,
                'enable_visual_analysis': True,
                'enable_text_analysis': True,
                'enable_crowd_detection': True,
                'enable_api_integration': True,
                'enable_intelligent_analysis': True,
                'enable_auto_optimization': True,
                'output_format': 'comprehensive'
            }
            
            logger.info("✅ تم تهيئة مولد الشورتس المتقدم بنجاح")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة النظام المتقدم: {e}")
            raise

    def generate_advanced_shorts(self, video_path: str, audio_path: str = None, 
                                text_content: str = None, timestamps: List[float] = None,
                                optimization_strategy: str = 'balanced') -> Dict[str, Any]:
        """إنتاج شورتس متقدم مع تحليل ذكي شامل"""
        try:
            logger.info("🚀 بدء إنتاج الشورتس المتقدم...")
            
            # التحقق من وجود الملفات
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"ملف الفيديو غير موجود: {video_path}")
            
            # مرحلة 1: التحليل الذكي الشامل
            logger.info("🧠 مرحلة التحليل الذكي...")
            intelligent_analysis = self.intelligence_engine.analyze_content_intelligently(
                video_path, audio_path, text_content, timestamps
            )
            
            # مرحلة 2: التحسين التلقائي
            logger.info("⚡ مرحلة التحسين التلقائي...")
            optimization_result = self.optimization_system.optimize_content_automatically(
                video_path, audio_path, text_content, timestamps, optimization_strategy
            )
            
            # مرحلة 3: التحليل المتخصص الإضافي
            logger.info("🔍 مرحلة التحليل المتخصص...")
            specialized_analysis = self._perform_specialized_analysis(
                video_path, audio_path, text_content, timestamps
            )
            
            # مرحلة 4: دمج النتائج
            logger.info("🔗 مرحلة دمج النتائج...")
            final_result = self._integrate_all_results(
                intelligent_analysis, optimization_result, specialized_analysis
            )
            
            # مرحلة 5: إنشاء التوصيات النهائية
            logger.info("💡 مرحلة التوصيات النهائية...")
            final_result['final_recommendations'] = self._generate_final_recommendations(final_result)
            
            logger.info("✅ تم إكمال إنتاج الشورتس المتقدم بنجاح")
            return final_result
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنتاج الشورتس المتقدم: {e}")
            return self._get_error_result(str(e))

    def _perform_specialized_analysis(self, video_path: str, audio_path: str = None,
                                    text_content: str = None, timestamps: List[float] = None) -> Dict[str, Any]:
        """تحليل متخصص إضافي"""
        try:
            specialized_results = {}
            
            # تحليل صوتي متخصص
            if self.system_config['enable_audio_analysis'] and (audio_path or video_path):
                logger.info("🎵 تحليل صوتي متخصص...")
                audio_result = self.audio_analyzer.analyze_audio_file(audio_path or video_path)
                specialized_results['detailed_audio'] = audio_result
            
            # تحليل بصري متخصص
            if self.system_config['enable_visual_analysis'] and video_path:
                logger.info("👁️ تحليل بصري متخصص...")
                visual_result = self.visual_analyzer.analyze_video_file(video_path)
                specialized_results['detailed_visual'] = visual_result
            
            # تحليل نصي متخصص
            if self.system_config['enable_text_analysis'] and text_content:
                logger.info("📝 تحليل نصي متخصص...")
                text_result = self.text_analyzer.analyze_text_comprehensive(text_content, timestamps)
                specialized_results['detailed_text'] = text_result
            
            # كشف جماعي متخصص
            if self.system_config['enable_crowd_detection'] and video_path:
                logger.info("👥 كشف جماعي متخصص...")
                crowd_result = self.crowd_detector.analyze_crowd_behavior(video_path, audio_path)
                specialized_results['detailed_crowd'] = crowd_result
            
            # تحليل APIs متعددة
            if self.system_config['enable_api_integration'] and text_content:
                logger.info("🌐 تحليل APIs متعددة...")
                api_result = self.api_manager.analyze_sentiment_multi_api(text_content)
                specialized_results['api_analysis'] = api_result
            
            return specialized_results
            
        except Exception as e:
            logger.error(f"خطأ في التحليل المتخصص: {e}")
            return {}

    def _integrate_all_results(self, intelligent_analysis, optimization_result, specialized_analysis) -> Dict[str, Any]:
        """دمج جميع النتائج"""
        try:
            integrated_result = {
                # النتائج الأساسية
                'intelligent_analysis': {
                    'overall_score': intelligent_analysis.overall_score,
                    'viral_potential': intelligent_analysis.viral_potential,
                    'engagement_potential': intelligent_analysis.engagement_potential,
                    'emotional_intensity': intelligent_analysis.emotional_intensity,
                    'content_quality': intelligent_analysis.content_quality,
                    'highlights_count': len(intelligent_analysis.highlights),
                    'insights': intelligent_analysis.insights,
                    'recommendations': intelligent_analysis.recommendations
                },
                
                # نتائج التحسين
                'optimization_result': {
                    'original_duration': optimization_result.original_duration,
                    'optimized_clips_count': len(optimization_result.optimized_clips),
                    'total_optimized_duration': optimization_result.total_optimized_duration,
                    'improvement_score': optimization_result.improvement_score,
                    'optimization_summary': optimization_result.optimization_summary,
                    'optimization_recommendations': optimization_result.recommendations
                },
                
                # المقاطع المحسنة
                'optimized_clips': [
                    {
                        'start_time': clip.start_time,
                        'end_time': clip.end_time,
                        'duration': clip.duration,
                        'optimization_score': clip.optimization_score,
                        'viral_potential': clip.viral_potential,
                        'engagement_score': clip.engagement_score,
                        'optimization_reasons': clip.optimization_reasons,
                        'recommended_effects': clip.recommended_effects
                    }
                    for clip in optimization_result.optimized_clips
                ],
                
                # اللحظات الذكية
                'intelligent_highlights': [
                    {
                        'start_time': highlight.start_time,
                        'end_time': highlight.end_time,
                        'peak_time': highlight.peak_time,
                        'type': highlight.highlight_type,
                        'confidence': highlight.confidence,
                        'viral_score': highlight.viral_score,
                        'engagement_score': highlight.engagement_score,
                        'emotional_impact': highlight.emotional_impact,
                        'reasoning': highlight.reasoning,
                        'tags': highlight.tags
                    }
                    for highlight in intelligent_analysis.highlights
                ],
                
                # التحليل المتخصص
                'specialized_analysis': specialized_analysis,
                
                # إحصائيات شاملة
                'comprehensive_stats': self._calculate_comprehensive_stats(
                    intelligent_analysis, optimization_result, specialized_analysis
                )
            }
            
            return integrated_result
            
        except Exception as e:
            logger.error(f"خطأ في دمج النتائج: {e}")
            return {}

    def _calculate_comprehensive_stats(self, intelligent_analysis, optimization_result, specialized_analysis) -> Dict[str, Any]:
        """حساب إحصائيات شاملة"""
        try:
            stats = {
                'analysis_completeness': 0.0,
                'data_sources_used': [],
                'total_highlights_found': 0,
                'total_clips_generated': 0,
                'average_confidence': 0.0,
                'quality_indicators': {}
            }
            
            # حساب اكتمال التحليل
            completeness_factors = []
            
            if intelligent_analysis.highlights:
                completeness_factors.append(1.0)
                stats['total_highlights_found'] = len(intelligent_analysis.highlights)
                stats['average_confidence'] = sum(h.confidence for h in intelligent_analysis.highlights) / len(intelligent_analysis.highlights)
            
            if optimization_result.optimized_clips:
                completeness_factors.append(1.0)
                stats['total_clips_generated'] = len(optimization_result.optimized_clips)
            
            if specialized_analysis:
                completeness_factors.append(1.0)
                stats['data_sources_used'] = list(specialized_analysis.keys())
            
            stats['analysis_completeness'] = sum(completeness_factors) / 3 if completeness_factors else 0.0
            
            # مؤشرات الجودة
            stats['quality_indicators'] = {
                'content_quality': intelligent_analysis.content_quality,
                'optimization_improvement': optimization_result.improvement_score,
                'viral_potential': intelligent_analysis.viral_potential,
                'engagement_potential': intelligent_analysis.engagement_potential
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"خطأ في حساب الإحصائيات: {e}")
            return {}

    def _generate_final_recommendations(self, integrated_result: Dict[str, Any]) -> List[str]:
        """إنشاء التوصيات النهائية"""
        try:
            recommendations = []
            
            # تحليل النتائج
            intelligent_analysis = integrated_result.get('intelligent_analysis', {})
            optimization_result = integrated_result.get('optimization_result', {})
            clips = integrated_result.get('optimized_clips', [])
            
            # توصيات بناءً على الجودة العامة
            overall_score = intelligent_analysis.get('overall_score', 0)
            if overall_score > 0.8:
                recommendations.append("🌟 محتوى ممتاز - جاهز للنشر الفوري")
            elif overall_score > 0.6:
                recommendations.append("✅ محتوى جيد - يمكن تحسينه بتأثيرات بسيطة")
            else:
                recommendations.append("⚠️ محتوى يحتاج تحسين - فكر في إعادة التحرير")
            
            # توصيات بناءً على الفيروسية
            viral_potential = intelligent_analysis.get('viral_potential', 0)
            if viral_potential > 0.7:
                recommendations.append("🚀 إمكانية فيروسية عالية - انشر في الأوقات الذروة")
            
            # توصيات بناءً على المقاطع
            if len(clips) > 3:
                recommendations.append("📹 عدد كبير من المقاطع - اختر الأفضل 2-3 منها")
            elif len(clips) < 2:
                recommendations.append("🔍 عدد قليل من المقاطع - ابحث عن لحظات إضافية")
            
            # توصيات بناءً على التحسين
            improvement_score = optimization_result.get('improvement_score', 0)
            if improvement_score > 0.7:
                recommendations.append("⚡ تحسين ممتاز - المقاطع محسنة بشكل مثالي")
            
            # توصيات تقنية
            if clips:
                best_clip = max(clips, key=lambda c: c.get('optimization_score', 0))
                recommendations.append(f"🎯 أفضل مقطع: {best_clip.get('start_time', 0):.1f}s - {best_clip.get('end_time', 0):.1f}s")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء التوصيات النهائية: {e}")
            return ["لا توجد توصيات محددة"]

    def _get_error_result(self, error_message: str) -> Dict[str, Any]:
        """إرجاع نتيجة خطأ"""
        return {
            'success': False,
            'error': error_message,
            'intelligent_analysis': {},
            'optimization_result': {},
            'optimized_clips': [],
            'intelligent_highlights': [],
            'specialized_analysis': {},
            'comprehensive_stats': {},
            'final_recommendations': [f"❌ فشل في التحليل: {error_message}"]
        }

    def get_system_status(self) -> Dict[str, Any]:
        """الحصول على حالة النظام"""
        try:
            return {
                'system_ready': True,
                'components_status': {
                    'audio_analyzer': bool(self.audio_analyzer),
                    'visual_analyzer': bool(self.visual_analyzer),
                    'text_analyzer': bool(self.text_analyzer),
                    'crowd_detector': bool(self.crowd_detector),
                    'api_manager': bool(self.api_manager),
                    'intelligence_engine': bool(self.intelligence_engine),
                    'optimization_system': bool(self.optimization_system)
                },
                'configuration': self.system_config,
                'api_status': self.api_manager.get_api_status() if self.api_manager else {}
            }
        except Exception as e:
            logger.error(f"خطأ في الحصول على حالة النظام: {e}")
            return {'system_ready': False, 'error': str(e)}

# دالة رئيسية للاستخدام المباشر
def generate_advanced_shorts(video_path: str, audio_path: str = None, 
                           text_content: str = None, timestamps: List[float] = None,
                           optimization_strategy: str = 'balanced') -> Dict[str, Any]:
    """دالة مبسطة لإنتاج الشورتس المتقدم"""
    try:
        generator = AdvancedShortsGenerator()
        return generator.generate_advanced_shorts(
            video_path, audio_path, text_content, timestamps, optimization_strategy
        )
    except Exception as e:
        logger.error(f"خطأ في إنتاج الشورتس المتقدم: {e}")
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    # اختبار النظام المتكامل
    print("🧪 اختبار نظام التكامل المتقدم")
    print("=" * 60)
    
    try:
        generator = AdvancedShortsGenerator()
        print("✅ تم تهيئة النظام المتكامل بنجاح")
        
        # عرض حالة النظام
        status = generator.get_system_status()
        print(f"🔧 حالة النظام: {'جاهز' if status['system_ready'] else 'غير جاهز'}")
        
        components = status.get('components_status', {})
        print("📊 حالة المكونات:")
        for component, status_val in components.items():
            print(f"   - {component}: {'✅' if status_val else '❌'}")
        
        print("\n🏁 انتهى الاختبار")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
