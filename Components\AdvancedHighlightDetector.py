#!/usr/bin/env python3
"""
كاشف اللقطات المتقدم
Advanced Highlight Detection System

يستخدم تحليل صوتي وبصري ونصي متقدم لاكتشاف اللحظات المثيرة
مدمج من الأداة المتقدمة مع تحسينات للأداة الحالية
"""

import cv2
import numpy as np
import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
import json
import time
import statistics

# محاولة استيراد المكتبات المتقدمة
try:
    import librosa
    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False
    librosa = None

try:
    from textblob import TextBlob
    TEXTBLOB_AVAILABLE = True
except ImportError:
    TEXTBLOB_AVAILABLE = False
    TextBlob = None

logger = logging.getLogger(__name__)

@dataclass
class HighlightMoment:
    """لحظة مميزة"""
    start_time: float
    end_time: float
    peak_time: float
    highlight_type: str
    confidence: float
    audio_features: Dict[str, float]
    visual_features: Dict[str, float]
    text_features: Dict[str, float]
    combined_score: float
    viral_potential: float

class AdvancedHighlightDetector:
    """كاشف اللقطات المتقدم"""
    
    def __init__(self):
        # إعدادات التحليل
        self.window_size = 5.0  # نافزة 5 ثوانٍ للتحليل
        self.overlap = 2.5      # تداخل 2.5 ثانية
        self.min_highlight_duration = 3.0  # أقل مدة للقطة
        self.max_highlight_duration = 60.0  # أقصى مدة للقطة
        
        # عتبات الكشف المحسنة
        self.audio_energy_threshold = 0.3
        self.visual_motion_threshold = 0.25
        self.text_sentiment_threshold = 0.4
        self.combined_score_threshold = 0.5
        self.viral_potential_threshold = 0.6
        
        # أوزان التحليل المختلفة
        self.weights = {
            'audio_energy': 0.25,
            'audio_tempo': 0.15,
            'visual_motion': 0.20,
            'visual_brightness': 0.10,
            'text_sentiment': 0.15,
            'text_keywords': 0.15
        }
        
        # كلمات مفتاحية للمحتوى الفيروسي
        self.viral_keywords = {
            'arabic': [
                'مذهل', 'رائع', 'لا أصدق', 'واو', 'يا إلهي', 'مستحيل', 
                'أول مرة', 'حصري', 'عاجل', 'مفاجأة', 'صدمة', 'إنجاز',
                'قياسي', 'تاريخي', 'نادر', 'فريد', 'استثنائي', 'خرافي',
                'OMG', 'WOW', 'Amazing', 'Incredible', 'Unbelievable'
            ],
            'english': [
                'amazing', 'incredible', 'unbelievable', 'wow', 'omg', 'shocking',
                'first time', 'exclusive', 'breaking', 'surprise', 'record',
                'historic', 'rare', 'unique', 'extraordinary', 'legendary',
                'viral', 'trending', 'epic', 'insane', 'mind-blowing'
            ]
        }
        
        # أنواع اللقطات المختلفة
        self.highlight_types = {
            'viral_moment': 'لحظة فيروسية',
            'emotional_peak': 'ذروة عاطفية',
            'action_sequence': 'تسلسل حركي',
            'surprise_reveal': 'كشف مفاجئ',
            'achievement_moment': 'لحظة إنجاز',
            'funny_moment': 'لحظة مضحكة',
            'dramatic_moment': 'لحظة دراماتيكية',
            'educational_key': 'نقطة تعليمية مهمة'
        }
        
        logger.info("تم تهيئة كاشف اللقطات المتقدم")
        logger.info(f"Librosa متوفر: {'نعم' if LIBROSA_AVAILABLE else 'لا'}")
        logger.info(f"TextBlob متوفر: {'نعم' if TEXTBLOB_AVAILABLE else 'لا'}")

    def detect_highlights_in_video(self, video_path: str, audio_path: str = None, 
                                 transcript: str = None) -> List[HighlightMoment]:
        """كشف اللقطات المميزة في الفيديو"""
        try:
            highlights = []
            
            # تحليل الفيديو للحصول على المعلومات الأساسية
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / fps
            
            logger.info(f"تحليل فيديو: {duration:.1f}s، {fps:.1f} fps")
            
            # تحليل متعدد الطبقات
            audio_analysis = self._analyze_audio_advanced(audio_path or video_path, duration)
            visual_analysis = self._analyze_visual_advanced(cap, fps, duration)
            text_analysis = self._analyze_text_advanced(transcript) if transcript else {}
            
            cap.release()
            
            # دمج التحليلات
            combined_analysis = self._combine_analyses(audio_analysis, visual_analysis, text_analysis, duration)
            
            # استخراج اللقطات المميزة
            highlights = self._extract_highlights_from_analysis(combined_analysis)
            
            # تحسين وترتيب اللقطات
            highlights = self._optimize_highlights(highlights)
            
            logger.info(f"تم اكتشاف {len(highlights)} لقطة مميزة")
            return highlights
            
        except Exception as e:
            logger.error(f"خطأ في كشف اللقطات المتقدم: {e}")
            return []

    def _analyze_audio_advanced(self, audio_path: str, duration: float) -> Dict[str, Any]:
        """تحليل صوتي متقدم باستخدام librosa"""
        try:
            if not LIBROSA_AVAILABLE:
                return self._analyze_audio_basic(audio_path, duration)
            
            # تحميل الملف الصوتي
            y, sr = librosa.load(audio_path, duration=duration)
            
            # تحليل الطاقة
            energy = librosa.feature.rms(y=y)[0]
            
            # تحليل الطيف الترددي
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
            
            # تحليل النبرة والإيقاع
            tempo, beats = librosa.beat.beat_track(y=y, sr=sr)
            
            # تحليل الكروما (النغمات الموسيقية)
            chroma = librosa.feature.chroma_stft(y=y, sr=sr)
            
            # تحليل MFCC (خصائص الصوت)
            mfccs = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)
            
            # تقسيم إلى نوافذ زمنية
            window_samples = int(self.window_size * sr)
            overlap_samples = int(self.overlap * sr)
            
            windows = []
            for start in range(0, len(y) - window_samples, overlap_samples):
                end = start + window_samples
                window_y = y[start:end]
                
                # حساب الخصائص لهذه النافذة
                window_energy = np.mean(librosa.feature.rms(y=window_y)[0])
                window_spectral = np.mean(librosa.feature.spectral_centroid(y=window_y, sr=sr)[0])
                window_tempo = librosa.beat.tempo(y=window_y, sr=sr)[0] if len(window_y) > sr else 0
                
                window_time = start / sr
                
                windows.append({
                    'time': window_time,
                    'energy': window_energy,
                    'spectral_centroid': window_spectral,
                    'tempo': window_tempo,
                    'duration': len(window_y) / sr
                })
            
            return {
                'windows': windows,
                'global_tempo': tempo,
                'total_beats': len(beats),
                'avg_energy': np.mean(energy),
                'avg_spectral': np.mean(spectral_centroids)
            }
            
        except Exception as e:
            logger.error(f"خطأ في التحليل الصوتي المتقدم: {e}")
            return self._analyze_audio_basic(audio_path, duration)

    def _analyze_audio_basic(self, audio_path: str, duration: float) -> Dict[str, Any]:
        """تحليل صوتي أساسي"""
        try:
            # تحليل أساسي باستخدام OpenCV
            cap = cv2.VideoCapture(audio_path)
            
            windows = []
            window_duration = self.window_size
            current_time = 0
            
            while current_time < duration:
                # تحليل أساسي جداً - سيتم تحسينه لاحقاً
                windows.append({
                    'time': current_time,
                    'energy': 0.5,  # قيمة افتراضية
                    'spectral_centroid': 0.5,
                    'tempo': 120,  # BPM افتراضي
                    'duration': window_duration
                })
                current_time += self.overlap
            
            cap.release()
            
            return {
                'windows': windows,
                'global_tempo': 120,
                'total_beats': int(duration * 2),
                'avg_energy': 0.5,
                'avg_spectral': 0.5
            }
            
        except Exception as e:
            logger.error(f"خطأ في التحليل الصوتي الأساسي: {e}")
            return {'windows': [], 'global_tempo': 120, 'total_beats': 0, 'avg_energy': 0.5, 'avg_spectral': 0.5}

    def _analyze_visual_advanced(self, cap: cv2.VideoCapture, fps: float, duration: float) -> Dict[str, Any]:
        """تحليل بصري متقدم"""
        try:
            windows = []
            window_frames = int(self.window_size * fps)
            overlap_frames = int(self.overlap * fps)
            
            frame_count = 0
            prev_frame = None
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                current_time = frame_count / fps
                
                # تحليل كل نافذة
                if frame_count % overlap_frames == 0:
                    window_analysis = self._analyze_visual_window(frame, prev_frame, current_time)
                    if window_analysis:
                        windows.append(window_analysis)
                
                prev_frame = frame.copy()
                frame_count += 1
            
            return {'windows': windows}
            
        except Exception as e:
            logger.error(f"خطأ في التحليل البصري المتقدم: {e}")
            return {'windows': []}

    def _analyze_visual_window(self, frame: np.ndarray, prev_frame: np.ndarray, time: float) -> Dict[str, Any]:
        """تحليل نافذة بصرية واحدة"""
        try:
            analysis = {
                'time': time,
                'motion': 0.0,
                'brightness': 0.0,
                'contrast': 0.0,
                'color_variance': 0.0,
                'edge_density': 0.0
            }
            
            # تحويل لرمادي
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # حساب السطوع
            analysis['brightness'] = np.mean(gray) / 255.0
            
            # حساب التباين
            analysis['contrast'] = np.std(gray) / 255.0
            
            # حساب كثافة الحواف
            edges = cv2.Canny(gray, 50, 150)
            analysis['edge_density'] = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
            
            # حساب تنوع الألوان
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            analysis['color_variance'] = np.std(hsv[:, :, 1]) / 255.0  # تشبع الألوان
            
            # حساب الحركة (إذا كان هناك إطار سابق)
            if prev_frame is not None:
                prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
                diff = cv2.absdiff(gray, prev_gray)
                analysis['motion'] = np.mean(diff) / 255.0
            
            return analysis
            
        except Exception as e:
            logger.error(f"خطأ في تحليل النافذة البصرية: {e}")
            return None

    def _analyze_text_advanced(self, transcript: str) -> Dict[str, Any]:
        """تحليل نصي متقدم"""
        try:
            if not transcript:
                return {}
            
            analysis = {
                'sentiment_scores': [],
                'keyword_matches': [],
                'emotional_words': [],
                'viral_potential_words': []
            }
            
            # تقسيم النص إلى جمل
            sentences = transcript.split('.')
            
            for i, sentence in enumerate(sentences):
                sentence = sentence.strip()
                if not sentence:
                    continue
                
                sentence_analysis = {
                    'index': i,
                    'text': sentence,
                    'sentiment': 0.0,
                    'keywords': [],
                    'viral_score': 0.0
                }
                
                # تحليل المشاعر
                if TEXTBLOB_AVAILABLE:
                    blob = TextBlob(sentence)
                    sentence_analysis['sentiment'] = blob.sentiment.polarity
                else:
                    # تحليل أساسي للمشاعر
                    sentence_analysis['sentiment'] = self._basic_sentiment_analysis(sentence)
                
                # البحث عن كلمات مفتاحية
                sentence_lower = sentence.lower()
                
                # كلمات فيروسية عربية
                for keyword in self.viral_keywords['arabic']:
                    if keyword.lower() in sentence_lower:
                        sentence_analysis['keywords'].append(keyword)
                        sentence_analysis['viral_score'] += 0.2
                
                # كلمات فيروسية إنجليزية
                for keyword in self.viral_keywords['english']:
                    if keyword.lower() in sentence_lower:
                        sentence_analysis['keywords'].append(keyword)
                        sentence_analysis['viral_score'] += 0.2
                
                # تطبيع النقاط
                sentence_analysis['viral_score'] = min(sentence_analysis['viral_score'], 1.0)
                
                analysis['sentiment_scores'].append(sentence_analysis['sentiment'])
                if sentence_analysis['keywords']:
                    analysis['keyword_matches'].append(sentence_analysis)
                if sentence_analysis['viral_score'] > 0:
                    analysis['viral_potential_words'].append(sentence_analysis)
            
            return analysis
            
        except Exception as e:
            logger.error(f"خطأ في التحليل النصي المتقدم: {e}")
            return {}

    def _basic_sentiment_analysis(self, text: str) -> float:
        """تحليل مشاعر أساسي"""
        try:
            positive_words = ['رائع', 'مذهل', 'جميل', 'ممتاز', 'great', 'amazing', 'awesome', 'fantastic']
            negative_words = ['سيء', 'فظيع', 'مروع', 'bad', 'terrible', 'awful', 'horrible']
            
            text_lower = text.lower()
            
            positive_count = sum(1 for word in positive_words if word in text_lower)
            negative_count = sum(1 for word in negative_words if word in text_lower)
            
            if positive_count + negative_count == 0:
                return 0.0
            
            return (positive_count - negative_count) / (positive_count + negative_count)
            
        except:
            return 0.0

    def _combine_analyses(self, audio_analysis: Dict, visual_analysis: Dict,
                         text_analysis: Dict, duration: float) -> Dict[str, Any]:
        """دمج جميع التحليلات"""
        try:
            combined_windows = []

            # الحصول على النوافذ من التحليلات المختلفة
            audio_windows = audio_analysis.get('windows', [])
            visual_windows = visual_analysis.get('windows', [])

            # دمج النوافذ بناءً على الوقت
            all_times = set()
            for window in audio_windows:
                all_times.add(round(window['time'], 1))
            for window in visual_windows:
                all_times.add(round(window['time'], 1))

            for time in sorted(all_times):
                # العثور على النوافذ المطابقة
                audio_window = self._find_closest_window(audio_windows, time)
                visual_window = self._find_closest_window(visual_windows, time)

                # دمج البيانات
                combined_window = {
                    'time': time,
                    'audio_energy': audio_window.get('energy', 0) if audio_window else 0,
                    'audio_tempo': audio_window.get('tempo', 0) if audio_window else 0,
                    'visual_motion': visual_window.get('motion', 0) if visual_window else 0,
                    'visual_brightness': visual_window.get('brightness', 0) if visual_window else 0,
                    'visual_contrast': visual_window.get('contrast', 0) if visual_window else 0,
                    'visual_edges': visual_window.get('edge_density', 0) if visual_window else 0,
                    'text_sentiment': self._get_text_sentiment_at_time(text_analysis, time),
                    'text_keywords': self._get_text_keywords_at_time(text_analysis, time),
                    'viral_potential': self._get_viral_potential_at_time(text_analysis, time)
                }

                # حساب النقاط المدمجة
                combined_window['combined_score'] = self._calculate_combined_score(combined_window)

                combined_windows.append(combined_window)

            return {
                'windows': combined_windows,
                'duration': duration,
                'audio_analysis': audio_analysis,
                'visual_analysis': visual_analysis,
                'text_analysis': text_analysis
            }

        except Exception as e:
            logger.error(f"خطأ في دمج التحليلات: {e}")
            return {'windows': [], 'duration': duration}

    def _find_closest_window(self, windows: List[Dict], target_time: float) -> Optional[Dict]:
        """العثور على أقرب نافذة لوقت محدد"""
        if not windows:
            return None

        closest_window = None
        min_diff = float('inf')

        for window in windows:
            diff = abs(window['time'] - target_time)
            if diff < min_diff:
                min_diff = diff
                closest_window = window

        return closest_window if min_diff <= self.window_size else None

    def _get_text_sentiment_at_time(self, text_analysis: Dict, time: float) -> float:
        """الحصول على مشاعر النص في وقت محدد"""
        try:
            sentiment_scores = text_analysis.get('sentiment_scores', [])
            if not sentiment_scores:
                return 0.0

            # تقدير بناءً على الوقت (تقسيم النص على المدة الكلية)
            index = int((time / 60) * len(sentiment_scores))  # افتراض دقيقة واحدة لكل جملة
            index = min(index, len(sentiment_scores) - 1)

            return sentiment_scores[index] if index >= 0 else 0.0

        except:
            return 0.0

    def _get_text_keywords_at_time(self, text_analysis: Dict, time: float) -> List[str]:
        """الحصول على كلمات مفتاحية في وقت محدد"""
        try:
            keyword_matches = text_analysis.get('keyword_matches', [])
            if not keyword_matches:
                return []

            # البحث عن الكلمات المفتاحية القريبة من هذا الوقت
            relevant_keywords = []
            for match in keyword_matches:
                # تقدير تقريبي للوقت
                estimated_time = match['index'] * 5  # افتراض 5 ثوانٍ لكل جملة
                if abs(estimated_time - time) <= self.window_size:
                    relevant_keywords.extend(match['keywords'])

            return relevant_keywords

        except:
            return []

    def _get_viral_potential_at_time(self, text_analysis: Dict, time: float) -> float:
        """الحصول على إمكانية الانتشار في وقت محدد"""
        try:
            viral_words = text_analysis.get('viral_potential_words', [])
            if not viral_words:
                return 0.0

            max_viral_score = 0.0
            for word_data in viral_words:
                estimated_time = word_data['index'] * 5
                if abs(estimated_time - time) <= self.window_size:
                    max_viral_score = max(max_viral_score, word_data['viral_score'])

            return max_viral_score

        except:
            return 0.0

    def _calculate_combined_score(self, window: Dict) -> float:
        """حساب النقاط المدمجة للنافذة"""
        try:
            score = 0.0

            # إضافة نقاط من كل مكون
            score += window.get('audio_energy', 0) * self.weights['audio_energy']
            score += min(window.get('audio_tempo', 0) / 140, 1.0) * self.weights['audio_tempo']  # تطبيع الإيقاع
            score += window.get('visual_motion', 0) * self.weights['visual_motion']
            score += window.get('visual_brightness', 0) * self.weights['visual_brightness']
            score += abs(window.get('text_sentiment', 0)) * self.weights['text_sentiment']  # القيمة المطلقة للمشاعر
            score += len(window.get('text_keywords', [])) * 0.1 * self.weights['text_keywords']

            # إضافة مكافأة للإمكانية الفيروسية
            viral_bonus = window.get('viral_potential', 0) * 0.3
            score += viral_bonus

            return min(score, 1.0)  # تطبيع النتيجة

        except Exception as e:
            logger.error(f"خطأ في حساب النقاط المدمجة: {e}")
            return 0.0

    def _extract_highlights_from_analysis(self, combined_analysis: Dict) -> List[HighlightMoment]:
        """استخراج اللقطات المميزة من التحليل المدمج"""
        try:
            windows = combined_analysis.get('windows', [])
            if not windows:
                return []

            highlights = []

            # البحث عن الذروات في النقاط
            scores = [w['combined_score'] for w in windows]
            times = [w['time'] for w in windows]

            # العثور على الذروات المحلية
            peaks = self._find_local_peaks(scores, times)

            for peak_time, peak_score in peaks:
                if peak_score >= self.combined_score_threshold:
                    # تحديد بداية ونهاية اللقطة
                    start_time, end_time = self._determine_highlight_boundaries(
                        windows, peak_time, peak_score
                    )

                    # تحديد نوع اللقطة
                    highlight_type = self._determine_highlight_type(windows, peak_time)

                    # حساب الثقة والإمكانية الفيروسية
                    confidence = min(peak_score, 1.0)
                    viral_potential = self._calculate_viral_potential(windows, start_time, end_time)

                    # إنشاء اللقطة المميزة
                    highlight = HighlightMoment(
                        start_time=start_time,
                        end_time=end_time,
                        peak_time=peak_time,
                        highlight_type=highlight_type,
                        confidence=confidence,
                        audio_features=self._extract_audio_features(windows, start_time, end_time),
                        visual_features=self._extract_visual_features(windows, start_time, end_time),
                        text_features=self._extract_text_features(windows, start_time, end_time),
                        combined_score=peak_score,
                        viral_potential=viral_potential
                    )

                    highlights.append(highlight)

            return highlights

        except Exception as e:
            logger.error(f"خطأ في استخراج اللقطات: {e}")
            return []

    def _find_local_peaks(self, scores: List[float], times: List[float]) -> List[Tuple[float, float]]:
        """العثور على الذروات المحلية"""
        try:
            peaks = []

            for i in range(1, len(scores) - 1):
                # فحص ما إذا كانت هذه النقطة ذروة محلية
                if scores[i] > scores[i-1] and scores[i] > scores[i+1]:
                    # فحص ما إذا كانت الذروة كبيرة بما فيه الكفاية
                    if scores[i] >= self.combined_score_threshold:
                        peaks.append((times[i], scores[i]))

            # ترتيب الذروات حسب النقاط (الأعلى أولاً)
            peaks.sort(key=lambda x: x[1], reverse=True)

            return peaks

        except Exception as e:
            logger.error(f"خطأ في العثور على الذروات: {e}")
            return []

    def _determine_highlight_boundaries(self, windows: List[Dict], peak_time: float,
                                      peak_score: float) -> Tuple[float, float]:
        """تحديد حدود اللقطة المميزة"""
        try:
            # العثور على نافذة الذروة
            peak_window_index = None
            for i, window in enumerate(windows):
                if abs(window['time'] - peak_time) < 0.1:
                    peak_window_index = i
                    break

            if peak_window_index is None:
                return peak_time, peak_time + self.min_highlight_duration

            # البحث عن البداية (عندما تنخفض النقاط)
            start_index = peak_window_index
            threshold = peak_score * 0.4  # 40% من نقاط الذروة

            for i in range(peak_window_index - 1, -1, -1):
                if windows[i]['combined_score'] < threshold:
                    break
                start_index = i

            # البحث عن النهاية
            end_index = peak_window_index
            for i in range(peak_window_index + 1, len(windows)):
                if windows[i]['combined_score'] < threshold:
                    break
                end_index = i

            start_time = windows[start_index]['time']
            end_time = windows[end_index]['time'] + self.window_size

            # التأكد من أن المدة مناسبة
            duration = end_time - start_time
            if duration < self.min_highlight_duration:
                end_time = start_time + self.min_highlight_duration
            elif duration > self.max_highlight_duration:
                end_time = start_time + self.max_highlight_duration

            return start_time, end_time

        except Exception as e:
            logger.error(f"خطأ في تحديد حدود اللقطة: {e}")
            return peak_time, peak_time + self.min_highlight_duration

    def _determine_highlight_type(self, windows: List[Dict], peak_time: float) -> str:
        """تحديد نوع اللقطة المميزة"""
        try:
            # العثور على النافذة المطابقة
            peak_window = None
            for window in windows:
                if abs(window['time'] - peak_time) < 0.1:
                    peak_window = window
                    break

            if not peak_window:
                return 'viral_moment'

            # تحليل الخصائص لتحديد النوع
            audio_energy = peak_window.get('audio_energy', 0)
            visual_motion = peak_window.get('visual_motion', 0)
            text_sentiment = peak_window.get('text_sentiment', 0)
            viral_potential = peak_window.get('viral_potential', 0)
            keywords = peak_window.get('text_keywords', [])

            # قواعد تحديد النوع
            if viral_potential > 0.7:
                return 'viral_moment'
            elif audio_energy > 0.8 and visual_motion > 0.7:
                return 'action_sequence'
            elif text_sentiment > 0.6:
                return 'emotional_peak'
            elif text_sentiment < -0.6:
                return 'dramatic_moment'
            elif any('مفاجأة' in k or 'surprise' in k.lower() for k in keywords):
                return 'surprise_reveal'
            elif any('إنجاز' in k or 'achievement' in k.lower() for k in keywords):
                return 'achievement_moment'
            elif any('مضحك' in k or 'funny' in k.lower() for k in keywords):
                return 'funny_moment'
            elif any('تعليم' in k or 'learn' in k.lower() for k in keywords):
                return 'educational_key'
            else:
                return 'viral_moment'

        except Exception as e:
            logger.error(f"خطأ في تحديد نوع اللقطة: {e}")
            return 'viral_moment'

    def _calculate_viral_potential(self, windows: List[Dict], start_time: float, end_time: float) -> float:
        """حساب إمكانية الانتشار الفيروسي"""
        try:
            viral_scores = []

            for window in windows:
                if start_time <= window['time'] <= end_time:
                    viral_score = window.get('viral_potential', 0)

                    # إضافة مكافآت للخصائص الفيروسية
                    if window.get('audio_energy', 0) > 0.7:
                        viral_score += 0.1
                    if window.get('visual_motion', 0) > 0.6:
                        viral_score += 0.1
                    if abs(window.get('text_sentiment', 0)) > 0.7:
                        viral_score += 0.15
                    if len(window.get('text_keywords', [])) > 2:
                        viral_score += 0.1

                    viral_scores.append(viral_score)

            return min(max(viral_scores) if viral_scores else 0.0, 1.0)

        except Exception as e:
            logger.error(f"خطأ في حساب الإمكانية الفيروسية: {e}")
            return 0.0

    def _extract_audio_features(self, windows: List[Dict], start_time: float, end_time: float) -> Dict[str, float]:
        """استخراج الخصائص الصوتية للقطة"""
        try:
            audio_features = {
                'avg_energy': 0.0,
                'max_energy': 0.0,
                'avg_tempo': 0.0,
                'energy_variance': 0.0
            }

            energies = []
            tempos = []

            for window in windows:
                if start_time <= window['time'] <= end_time:
                    energies.append(window.get('audio_energy', 0))
                    tempos.append(window.get('audio_tempo', 0))

            if energies:
                audio_features['avg_energy'] = statistics.mean(energies)
                audio_features['max_energy'] = max(energies)
                audio_features['energy_variance'] = statistics.stdev(energies) if len(energies) > 1 else 0

            if tempos:
                audio_features['avg_tempo'] = statistics.mean(tempos)

            return audio_features

        except Exception as e:
            logger.error(f"خطأ في استخراج الخصائص الصوتية: {e}")
            return {}

    def _extract_visual_features(self, windows: List[Dict], start_time: float, end_time: float) -> Dict[str, float]:
        """استخراج الخصائص البصرية للقطة"""
        try:
            visual_features = {
                'avg_motion': 0.0,
                'max_motion': 0.0,
                'avg_brightness': 0.0,
                'avg_contrast': 0.0,
                'motion_variance': 0.0
            }

            motions = []
            brightnesses = []
            contrasts = []

            for window in windows:
                if start_time <= window['time'] <= end_time:
                    motions.append(window.get('visual_motion', 0))
                    brightnesses.append(window.get('visual_brightness', 0))
                    contrasts.append(window.get('visual_contrast', 0))

            if motions:
                visual_features['avg_motion'] = statistics.mean(motions)
                visual_features['max_motion'] = max(motions)
                visual_features['motion_variance'] = statistics.stdev(motions) if len(motions) > 1 else 0

            if brightnesses:
                visual_features['avg_brightness'] = statistics.mean(brightnesses)

            if contrasts:
                visual_features['avg_contrast'] = statistics.mean(contrasts)

            return visual_features

        except Exception as e:
            logger.error(f"خطأ في استخراج الخصائص البصرية: {e}")
            return {}

    def _extract_text_features(self, windows: List[Dict], start_time: float, end_time: float) -> Dict[str, Any]:
        """استخراج الخصائص النصية للقطة"""
        try:
            text_features = {
                'avg_sentiment': 0.0,
                'sentiment_range': 0.0,
                'total_keywords': 0,
                'unique_keywords': [],
                'max_viral_potential': 0.0
            }

            sentiments = []
            all_keywords = []
            viral_potentials = []

            for window in windows:
                if start_time <= window['time'] <= end_time:
                    sentiments.append(window.get('text_sentiment', 0))
                    all_keywords.extend(window.get('text_keywords', []))
                    viral_potentials.append(window.get('viral_potential', 0))

            if sentiments:
                text_features['avg_sentiment'] = statistics.mean(sentiments)
                text_features['sentiment_range'] = max(sentiments) - min(sentiments)

            if all_keywords:
                text_features['total_keywords'] = len(all_keywords)
                text_features['unique_keywords'] = list(set(all_keywords))

            if viral_potentials:
                text_features['max_viral_potential'] = max(viral_potentials)

            return text_features

        except Exception as e:
            logger.error(f"خطأ في استخراج الخصائص النصية: {e}")
            return {}

    def _optimize_highlights(self, highlights: List[HighlightMoment]) -> List[HighlightMoment]:
        """تحسين وترتيب اللقطات المميزة"""
        try:
            if not highlights:
                return []

            # إزالة التداخلات
            optimized = []
            sorted_highlights = sorted(highlights, key=lambda x: x.start_time)

            for highlight in sorted_highlights:
                # فحص التداخل مع اللقطات الموجودة
                overlaps = False
                for existing in optimized:
                    if (highlight.start_time < existing.end_time and
                        highlight.end_time > existing.start_time):
                        # دمج اللقطات المتداخلة (اختيار الأفضل)
                        if highlight.combined_score > existing.combined_score:
                            optimized.remove(existing)
                            optimized.append(highlight)
                        overlaps = True
                        break

                if not overlaps:
                    optimized.append(highlight)

            # ترتيب حسب الجودة والإمكانية الفيروسية
            optimized.sort(key=lambda x: (x.viral_potential, x.combined_score), reverse=True)

            # الاحتفاظ بأفضل اللقطات فقط (حد أقصى 10)
            return optimized[:10]

        except Exception as e:
            logger.error(f"خطأ في تحسين اللقطات: {e}")
            return highlights

# دالة للتوافق مع الأداة الحالية
def detect_highlights_advanced(video_path: str, audio_path: str = None, transcript: str = None) -> List[Dict[str, Any]]:
    """دالة مبسطة لكشف اللقطات المتقدمة"""
    try:
        detector = AdvancedHighlightDetector()
        highlights = detector.detect_highlights_in_video(video_path, audio_path, transcript)
        
        # تحويل إلى تنسيق مبسط
        simple_highlights = []
        for highlight in highlights:
            simple_highlights.append({
                'start_time': highlight.start_time,
                'end_time': highlight.end_time,
                'type': highlight.highlight_type,
                'confidence': highlight.confidence,
                'viral_potential': highlight.viral_potential
            })
        
        return simple_highlights
        
    except Exception as e:
        logger.error(f"خطأ في كشف اللقطات المتقدم: {e}")
        return []

if __name__ == "__main__":
    # اختبار النظام
    print("🧪 اختبار كاشف اللقطات المتقدم")
    print("=" * 50)
    
    detector = AdvancedHighlightDetector()
    print(f"✅ تم تهيئة الكاشف")
    print(f"📊 Librosa متوفر: {'نعم' if LIBROSA_AVAILABLE else 'لا'}")
    print(f"📝 TextBlob متوفر: {'نعم' if TEXTBLOB_AVAILABLE else 'لا'}")
    print(f"🎯 أنواع اللقطات المدعومة: {len(detector.highlight_types)}")
    
    # عرض أنواع اللقطات
    print("\n🎬 أنواع اللقطات المدعومة:")
    for key, value in detector.highlight_types.items():
        print(f"   - {key}: {value}")
    
    print(f"\n🔍 كلمات مفتاحية فيروسية: {len(detector.viral_keywords['arabic']) + len(detector.viral_keywords['english'])}")
    print("\n🏁 انتهى الاختبار")
