#!/usr/bin/env python3
"""
قاعدة بيانات محلية لحفظ المقاطع المعالجة
AI Youtube Shorts Generator - Database Module
"""

import json
import os
from datetime import datetime
from typing import List, Dict, Optional

class VideoDatabase:
    """قاعدة بيانات محلية للفيديوهات المعالجة"""
    
    def __init__(self, db_file: str = "processed_videos.json"):
        self.db_file = db_file
        self.videos = self._load_database()
    
    def _load_database(self) -> List[Dict]:
        """تحميل قاعدة البيانات من الملف"""
        try:
            if os.path.exists(self.db_file):
                with open(self.db_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            print(f"خطأ في تحميل قاعدة البيانات: {e}")
            return []
    
    def _save_database(self) -> bool:
        """حفظ قاعدة البيانات في الملف"""
        try:
            with open(self.db_file, 'w', encoding='utf-8') as f:
                json.dump(self.videos, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"خطأ في حفظ قاعدة البيانات: {e}")
            return False
    
    def add_video(self, video_data: Dict) -> bool:
        """إضافة فيديو جديد إلى قاعدة البيانات"""
        try:
            # إضافة معرف فريد ووقت الإنشاء
            video_data['id'] = len(self.videos) + 1
            video_data['created_at'] = datetime.now().isoformat()
            video_data['updated_at'] = datetime.now().isoformat()
            
            # إضافة الفيديو إلى القائمة
            self.videos.append(video_data)
            
            # حفظ في الملف
            return self._save_database()
        except Exception as e:
            print(f"خطأ في إضافة الفيديو: {e}")
            return False
    
    def get_all_videos(self) -> List[Dict]:
        """الحصول على جميع الفيديوهات"""
        return self.videos
    
    def get_video_by_id(self, video_id: int) -> Optional[Dict]:
        """الحصول على فيديو بواسطة المعرف"""
        for video in self.videos:
            if video.get('id') == video_id:
                return video
        return None
    
    def update_video(self, video_id: int, updates: Dict) -> bool:
        """تحديث بيانات فيديو"""
        try:
            for i, video in enumerate(self.videos):
                if video.get('id') == video_id:
                    # تحديث البيانات
                    self.videos[i].update(updates)
                    self.videos[i]['updated_at'] = datetime.now().isoformat()
                    return self._save_database()
            return False
        except Exception as e:
            print(f"خطأ في تحديث الفيديو: {e}")
            return False
    
    def delete_video(self, video_id: int) -> bool:
        """حذف فيديو من قاعدة البيانات"""
        try:
            self.videos = [v for v in self.videos if v.get('id') != video_id]
            return self._save_database()
        except Exception as e:
            print(f"خطأ في حذف الفيديو: {e}")
            return False
    
    def search_videos(self, query: str) -> List[Dict]:
        """البحث في الفيديوهات"""
        results = []
        query_lower = query.lower()
        
        for video in self.videos:
            # البحث في العنوان والنص المنسوخ
            if (query_lower in video.get('title', '').lower() or 
                query_lower in video.get('transcript', '').lower()):
                results.append(video)
        
        return results
    
    def get_videos_by_date(self, start_date: str, end_date: str) -> List[Dict]:
        """الحصول على الفيديوهات في فترة زمنية محددة"""
        results = []
        
        for video in self.videos:
            video_date = video.get('created_at', '')
            if start_date <= video_date <= end_date:
                results.append(video)
        
        return results
    
    def get_statistics(self) -> Dict:
        """الحصول على إحصائيات قاعدة البيانات"""
        total_videos = len(self.videos)
        total_shorts = sum(len(video.get('shorts', [])) for video in self.videos)
        
        # حساب الفيديوهات حسب التاريخ
        dates = {}
        for video in self.videos:
            date = video.get('created_at', '')[:10]  # YYYY-MM-DD
            dates[date] = dates.get(date, 0) + 1
        
        return {
            'total_videos': total_videos,
            'total_shorts': total_shorts,
            'videos_by_date': dates,
            'last_updated': datetime.now().isoformat()
        }

class ShortsDatabase:
    """قاعدة بيانات للمقاطع القصيرة المنشأة"""
    
    def __init__(self, db_file: str = "generated_shorts.json"):
        self.db_file = db_file
        self.shorts = self._load_database()
    
    def _load_database(self) -> List[Dict]:
        """تحميل قاعدة البيانات من الملف"""
        try:
            if os.path.exists(self.db_file):
                with open(self.db_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            print(f"خطأ في تحميل قاعدة بيانات المقاطع: {e}")
            return []
    
    def _save_database(self) -> bool:
        """حفظ قاعدة البيانات في الملف"""
        try:
            with open(self.db_file, 'w', encoding='utf-8') as f:
                json.dump(self.shorts, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"خطأ في حفظ قاعدة بيانات المقاطع: {e}")
            return False
    
    def add_short(self, short_data: Dict) -> bool:
        """إضافة مقطع قصير جديد"""
        try:
            short_data['id'] = len(self.shorts) + 1
            short_data['created_at'] = datetime.now().isoformat()
            
            self.shorts.append(short_data)
            return self._save_database()
        except Exception as e:
            print(f"خطأ في إضافة المقطع القصير: {e}")
            return False
    
    def get_all_shorts(self) -> List[Dict]:
        """الحصول على جميع المقاطع القصيرة"""
        return self.shorts
    
    def get_shorts_by_video_id(self, video_id: int) -> List[Dict]:
        """الحصول على المقاطع القصيرة لفيديو معين"""
        return [s for s in self.shorts if s.get('video_id') == video_id]

    def delete_short(self, short_id: int) -> bool:
        """حذف مقطع قصير"""
        try:
            self.shorts = [s for s in self.shorts if s.get('id') != short_id]
            return self._save_database()
        except Exception as e:
            print(f"خطأ في حذف المقطع القصير: {e}")
            return False

# إنشاء مثيلات قواعد البيانات
video_db = VideoDatabase()
shorts_db = ShortsDatabase()
