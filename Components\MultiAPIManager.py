#!/usr/bin/env python3
"""
مدير APIs متعددة
Multi-API Manager System

يدير الاتصال مع APIs متعددة مثل HuggingFace وGoogle Cloud وAssemblyAI
مدمج من الأداة المتقدمة مع تحسينات للأداة الحالية
"""

import os
import json
import time
import logging
import requests
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
import base64
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

logger = logging.getLogger(__name__)

@dataclass
class APIResponse:
    """استجابة API"""
    success: bool
    data: Any
    error_message: str
    api_name: str
    response_time: float
    tokens_used: int = 0

@dataclass
class APIConfig:
    """إعدادات API"""
    name: str
    base_url: str
    api_key: str
    rate_limit: int  # طلبات في الدقيقة
    max_retries: int = 3
    timeout: int = 30

class MultiAPIManager:
    """مدير APIs متعددة"""
    
    def __init__(self):
        # إعداد APIs المختلفة
        self.apis = {
            'huggingface': self._setup_huggingface(),
            'google_cloud': self._setup_google_cloud(),
            'assemblyai': self._setup_assemblyai(),
            'openai': self._setup_openai(),
            'anthropic': self._setup_anthropic()
        }
        
        # تتبع الاستخدام
        self.usage_tracker = {
            api_name: {
                'requests_today': 0,
                'tokens_used': 0,
                'last_request': 0,
                'errors': 0,
                'success_rate': 1.0
            }
            for api_name in self.apis.keys()
        }
        
        # ملف تتبع الاستخدام
        self.usage_file = Path("data/api_usage.json")
        self.usage_file.parent.mkdir(exist_ok=True)
        self._load_usage_data()
        
        # إعدادات عامة
        self.fallback_order = ['huggingface', 'google_cloud', 'assemblyai', 'openai', 'anthropic']
        self.auto_fallback = True
        
        logger.info("تم تهيئة مدير APIs المتعددة")
        self._log_api_status()

    def _setup_huggingface(self) -> Optional[APIConfig]:
        """إعداد HuggingFace API"""
        try:
            api_key = os.getenv('HUGGINGFACE_API_KEY')
            if not api_key:
                logger.warning("مفتاح HuggingFace API غير موجود")
                return None
            
            return APIConfig(
                name='huggingface',
                base_url='https://api-inference.huggingface.co',
                api_key=api_key,
                rate_limit=1000,  # 1000 طلب في الدقيقة
                max_retries=3,
                timeout=30
            )
        except Exception as e:
            logger.error(f"خطأ في إعداد HuggingFace: {e}")
            return None

    def _setup_google_cloud(self) -> Optional[APIConfig]:
        """إعداد Google Cloud API"""
        try:
            api_key = os.getenv('GOOGLE_CLOUD_API_KEY')
            if not api_key:
                logger.warning("مفتاح Google Cloud API غير موجود")
                return None
            
            return APIConfig(
                name='google_cloud',
                base_url='https://speech.googleapis.com/v1',
                api_key=api_key,
                rate_limit=600,  # 600 طلب في الدقيقة
                max_retries=3,
                timeout=60
            )
        except Exception as e:
            logger.error(f"خطأ في إعداد Google Cloud: {e}")
            return None

    def _setup_assemblyai(self) -> Optional[APIConfig]:
        """إعداد AssemblyAI API"""
        try:
            api_key = os.getenv('ASSEMBLYAI_API_KEY')
            if not api_key:
                logger.warning("مفتاح AssemblyAI API غير موجود")
                return None
            
            return APIConfig(
                name='assemblyai',
                base_url='https://api.assemblyai.com/v2',
                api_key=api_key,
                rate_limit=100,  # 100 طلب في الدقيقة
                max_retries=3,
                timeout=120
            )
        except Exception as e:
            logger.error(f"خطأ في إعداد AssemblyAI: {e}")
            return None

    def _setup_openai(self) -> Optional[APIConfig]:
        """إعداد OpenAI API"""
        try:
            api_key = os.getenv('OPENAI_API_KEY')
            if not api_key:
                logger.warning("مفتاح OpenAI API غير موجود")
                return None
            
            return APIConfig(
                name='openai',
                base_url='https://api.openai.com/v1',
                api_key=api_key,
                rate_limit=3000,  # 3000 طلب في الدقيقة
                max_retries=3,
                timeout=60
            )
        except Exception as e:
            logger.error(f"خطأ في إعداد OpenAI: {e}")
            return None

    def _setup_anthropic(self) -> Optional[APIConfig]:
        """إعداد Anthropic API"""
        try:
            api_key = os.getenv('ANTHROPIC_API_KEY')
            if not api_key:
                logger.warning("مفتاح Anthropic API غير موجود")
                return None
            
            return APIConfig(
                name='anthropic',
                base_url='https://api.anthropic.com/v1',
                api_key=api_key,
                rate_limit=1000,  # 1000 طلب في الدقيقة
                max_retries=3,
                timeout=60
            )
        except Exception as e:
            logger.error(f"خطأ في إعداد Anthropic: {e}")
            return None

    def _load_usage_data(self):
        """تحميل بيانات الاستخدام"""
        try:
            if self.usage_file.exists():
                with open(self.usage_file, 'r', encoding='utf-8') as f:
                    saved_data = json.load(f)
                    
                    # دمج البيانات المحفوظة
                    for api_name, data in saved_data.items():
                        if api_name in self.usage_tracker:
                            self.usage_tracker[api_name].update(data)
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات الاستخدام: {e}")

    def _save_usage_data(self):
        """حفظ بيانات الاستخدام"""
        try:
            with open(self.usage_file, 'w', encoding='utf-8') as f:
                json.dump(self.usage_tracker, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات الاستخدام: {e}")

    def _log_api_status(self):
        """عرض حالة APIs"""
        available_apis = [name for name, config in self.apis.items() if config is not None]
        logger.info(f"APIs المتاحة: {', '.join(available_apis)}")
        
        for api_name, config in self.apis.items():
            if config:
                usage = self.usage_tracker[api_name]
                logger.info(f"{api_name}: {usage['requests_today']} طلب اليوم، معدل النجاح: {usage['success_rate']:.2%}")

    def analyze_sentiment_multi_api(self, text: str, preferred_api: str = None) -> APIResponse:
        """تحليل المشاعر باستخدام APIs متعددة"""
        try:
            # ترتيب APIs للمحاولة
            api_order = [preferred_api] if preferred_api and preferred_api in self.apis else []
            api_order.extend([api for api in self.fallback_order if api not in api_order])
            
            for api_name in api_order:
                if not self.apis[api_name]:
                    continue
                
                # فحص حدود الاستخدام
                if not self._check_rate_limit(api_name):
                    continue
                
                # محاولة التحليل
                response = self._analyze_sentiment_with_api(text, api_name)
                if response.success:
                    self._update_usage_stats(api_name, True, response.tokens_used)
                    return response
                else:
                    self._update_usage_stats(api_name, False)
                    logger.warning(f"فشل {api_name}: {response.error_message}")
            
            # إذا فشلت جميع APIs
            return APIResponse(
                success=False,
                data=None,
                error_message="فشل في جميع APIs المتاحة",
                api_name="none",
                response_time=0.0
            )
            
        except Exception as e:
            logger.error(f"خطأ في تحليل المشاعر متعدد APIs: {e}")
            return APIResponse(
                success=False,
                data=None,
                error_message=str(e),
                api_name="error",
                response_time=0.0
            )

    def _analyze_sentiment_with_api(self, text: str, api_name: str) -> APIResponse:
        """تحليل المشاعر مع API محدد"""
        start_time = time.time()
        
        try:
            if api_name == 'huggingface':
                return self._huggingface_sentiment(text, start_time)
            elif api_name == 'google_cloud':
                return self._google_cloud_sentiment(text, start_time)
            elif api_name == 'openai':
                return self._openai_sentiment(text, start_time)
            else:
                return APIResponse(
                    success=False,
                    data=None,
                    error_message=f"API غير مدعوم: {api_name}",
                    api_name=api_name,
                    response_time=time.time() - start_time
                )
                
        except Exception as e:
            return APIResponse(
                success=False,
                data=None,
                error_message=str(e),
                api_name=api_name,
                response_time=time.time() - start_time
            )

    def _huggingface_sentiment(self, text: str, start_time: float) -> APIResponse:
        """تحليل المشاعر باستخدام HuggingFace"""
        try:
            config = self.apis['huggingface']
            
            headers = {
                'Authorization': f'Bearer {config.api_key}',
                'Content-Type': 'application/json'
            }
            
            # استخدام نموذج تحليل المشاعر
            url = f"{config.base_url}/models/cardiffnlp/twitter-roberta-base-sentiment-latest"
            
            payload = {
                "inputs": text,
                "options": {"wait_for_model": True}
            }
            
            response = requests.post(
                url, 
                headers=headers, 
                json=payload, 
                timeout=config.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                
                # تحويل النتيجة إلى تنسيق موحد
                sentiment_data = {
                    'sentiment': result[0]['label'].lower(),
                    'confidence': result[0]['score'],
                    'raw_response': result
                }
                
                return APIResponse(
                    success=True,
                    data=sentiment_data,
                    error_message="",
                    api_name='huggingface',
                    response_time=time.time() - start_time,
                    tokens_used=len(text.split())
                )
            else:
                return APIResponse(
                    success=False,
                    data=None,
                    error_message=f"HTTP {response.status_code}: {response.text}",
                    api_name='huggingface',
                    response_time=time.time() - start_time
                )
                
        except Exception as e:
            return APIResponse(
                success=False,
                data=None,
                error_message=str(e),
                api_name='huggingface',
                response_time=time.time() - start_time
            )

    def _google_cloud_sentiment(self, text: str, start_time: float) -> APIResponse:
        """تحليل المشاعر باستخدام Google Cloud"""
        try:
            config = self.apis['google_cloud']
            
            url = f"https://language.googleapis.com/v1/documents:analyzeSentiment?key={config.api_key}"
            
            payload = {
                "document": {
                    "type": "PLAIN_TEXT",
                    "content": text
                },
                "encodingType": "UTF8"
            }
            
            response = requests.post(
                url,
                json=payload,
                timeout=config.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                
                sentiment_data = {
                    'sentiment': 'positive' if result['documentSentiment']['score'] > 0 else 'negative' if result['documentSentiment']['score'] < 0 else 'neutral',
                    'confidence': abs(result['documentSentiment']['score']),
                    'magnitude': result['documentSentiment']['magnitude'],
                    'raw_response': result
                }
                
                return APIResponse(
                    success=True,
                    data=sentiment_data,
                    error_message="",
                    api_name='google_cloud',
                    response_time=time.time() - start_time,
                    tokens_used=len(text.split())
                )
            else:
                return APIResponse(
                    success=False,
                    data=None,
                    error_message=f"HTTP {response.status_code}: {response.text}",
                    api_name='google_cloud',
                    response_time=time.time() - start_time
                )
                
        except Exception as e:
            return APIResponse(
                success=False,
                data=None,
                error_message=str(e),
                api_name='google_cloud',
                response_time=time.time() - start_time
            )

    def _openai_sentiment(self, text: str, start_time: float) -> APIResponse:
        """تحليل المشاعر باستخدام OpenAI"""
        try:
            config = self.apis['openai']
            
            headers = {
                'Authorization': f'Bearer {config.api_key}',
                'Content-Type': 'application/json'
            }
            
            url = f"{config.base_url}/chat/completions"
            
            payload = {
                "model": "gpt-3.5-turbo",
                "messages": [
                    {
                        "role": "system",
                        "content": "أنت محلل مشاعر. حلل المشاعر في النص وأرجع النتيجة في JSON مع sentiment (positive/negative/neutral) و confidence (0-1)."
                    },
                    {
                        "role": "user",
                        "content": f"حلل مشاعر هذا النص: {text}"
                    }
                ],
                "max_tokens": 100,
                "temperature": 0.1
            }
            
            response = requests.post(
                url,
                headers=headers,
                json=payload,
                timeout=config.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                # محاولة تحليل JSON
                try:
                    sentiment_data = json.loads(content)
                except:
                    # إذا فشل التحليل، استخدم تحليل أساسي
                    sentiment_data = {
                        'sentiment': 'neutral',
                        'confidence': 0.5,
                        'raw_response': content
                    }
                
                return APIResponse(
                    success=True,
                    data=sentiment_data,
                    error_message="",
                    api_name='openai',
                    response_time=time.time() - start_time,
                    tokens_used=result['usage']['total_tokens']
                )
            else:
                return APIResponse(
                    success=False,
                    data=None,
                    error_message=f"HTTP {response.status_code}: {response.text}",
                    api_name='openai',
                    response_time=time.time() - start_time
                )
                
        except Exception as e:
            return APIResponse(
                success=False,
                data=None,
                error_message=str(e),
                api_name='openai',
                response_time=time.time() - start_time
            )

    def _check_rate_limit(self, api_name: str) -> bool:
        """فحص حدود الاستخدام"""
        try:
            config = self.apis[api_name]
            usage = self.usage_tracker[api_name]

            current_time = time.time()

            # إعادة تعيين العداد اليومي (كل 24 ساعة)
            if current_time - usage['last_request'] > 86400:  # 24 ساعة
                usage['requests_today'] = 0

            # فحص الحد اليومي (افتراضي: 80% من الحد الأقصى)
            daily_limit = config.rate_limit * 24 * 60 * 0.8  # 80% من الحد اليومي
            if usage['requests_today'] >= daily_limit:
                logger.warning(f"تم تجاوز الحد اليومي لـ {api_name}")
                return False

            # فحص الحد في الدقيقة
            if current_time - usage['last_request'] < 60:  # أقل من دقيقة
                requests_per_minute = usage['requests_today'] / max((current_time - usage['last_request']) / 60, 1)
                if requests_per_minute >= config.rate_limit:
                    logger.warning(f"تم تجاوز حد الدقيقة لـ {api_name}")
                    return False

            return True

        except Exception as e:
            logger.error(f"خطأ في فحص حدود الاستخدام: {e}")
            return False

    def _update_usage_stats(self, api_name: str, success: bool, tokens_used: int = 0):
        """تحديث إحصائيات الاستخدام"""
        try:
            usage = self.usage_tracker[api_name]

            usage['requests_today'] += 1
            usage['tokens_used'] += tokens_used
            usage['last_request'] = time.time()

            if not success:
                usage['errors'] += 1

            # حساب معدل النجاح
            total_requests = usage['requests_today']
            successful_requests = total_requests - usage['errors']
            usage['success_rate'] = successful_requests / max(total_requests, 1)

            # حفظ البيانات
            self._save_usage_data()

        except Exception as e:
            logger.error(f"خطأ في تحديث إحصائيات الاستخدام: {e}")

    def get_api_status(self) -> Dict[str, Any]:
        """الحصول على حالة جميع APIs"""
        try:
            status = {
                'available_apis': [],
                'unavailable_apis': [],
                'usage_summary': {},
                'recommendations': []
            }

            for api_name, config in self.apis.items():
                if config:
                    status['available_apis'].append(api_name)
                    usage = self.usage_tracker[api_name]
                    status['usage_summary'][api_name] = {
                        'requests_today': usage['requests_today'],
                        'success_rate': f"{usage['success_rate']:.1%}",
                        'tokens_used': usage['tokens_used'],
                        'status': 'healthy' if usage['success_rate'] > 0.8 else 'degraded'
                    }
                else:
                    status['unavailable_apis'].append(api_name)

            # توصيات
            if not status['available_apis']:
                status['recommendations'].append("لا توجد APIs متاحة - تحقق من مفاتيح API")

            best_api = max(
                status['available_apis'],
                key=lambda api: self.usage_tracker[api]['success_rate'],
                default=None
            )

            if best_api:
                status['recommendations'].append(f"أفضل API حالياً: {best_api}")

            return status

        except Exception as e:
            logger.error(f"خطأ في الحصول على حالة APIs: {e}")
            return {}

# دوال مساعدة للتوافق مع الأداة الحالية
def analyze_with_multiple_apis(text: str, analysis_type: str = 'sentiment', preferred_api: str = None) -> Dict[str, Any]:
    """تحليل باستخدام APIs متعددة"""
    try:
        manager = MultiAPIManager()
        
        if analysis_type == 'sentiment':
            response = manager.analyze_sentiment_multi_api(text, preferred_api)
            
            return {
                'success': response.success,
                'data': response.data,
                'api_used': response.api_name,
                'response_time': response.response_time,
                'error': response.error_message if not response.success else None
            }
        else:
            return {
                'success': False,
                'error': f'نوع التحليل غير مدعوم: {analysis_type}'
            }
            
    except Exception as e:
        logger.error(f"خطأ في التحليل متعدد APIs: {e}")
        return {
            'success': False,
            'error': str(e)
        }

if __name__ == "__main__":
    # اختبار النظام
    print("🧪 اختبار مدير APIs المتعددة")
    print("=" * 50)
    
    manager = MultiAPIManager()
    print(f"✅ تم تهيئة المدير")
    
    available_apis = [name for name, config in manager.apis.items() if config is not None]
    print(f"📊 APIs المتاحة: {', '.join(available_apis)}")
    
    # اختبار تحليل المشاعر
    test_text = "هذا فيديو رائع ومذهل! أحببته كثيراً."
    print(f"\n🧪 اختبار تحليل المشاعر:")
    print(f"النص: {test_text}")
    
    response = manager.analyze_sentiment_multi_api(test_text)
    print(f"النجاح: {response.success}")
    print(f"API المستخدم: {response.api_name}")
    print(f"وقت الاستجابة: {response.response_time:.2f}s")
    
    if response.success:
        print(f"النتيجة: {response.data}")
    else:
        print(f"الخطأ: {response.error_message}")
    
    print("\n🏁 انتهى الاختبار")
