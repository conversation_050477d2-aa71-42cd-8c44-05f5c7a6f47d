#!/usr/bin/env python3
"""
مولد الشورتس المبسط
Simple Shorts Generator

نظام مبسط وفعال لإنتاج مقاطع الشورتس المثيرة
يركز على النتائج العملية والسرعة
"""

import os
import cv2
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
import json
import time

# استيراد الكاشف المبسط
try:
    from .SimpleHighlightDetector import SimpleHighlightDetector, detect_exciting_clips
except ImportError:
    from SimpleHighlightDetector import SimpleHighlightDetector, detect_exciting_clips

logger = logging.getLogger(__name__)

class SimpleShortsGenerator:
    """مولد الشورتس المبسط"""
    
    def __init__(self):
        # المكونات الأساسية
        self.highlight_detector = SimpleHighlightDetector()
        
        # إعدادات الإنتاج
        self.default_target_clips = 3
        self.max_clips = 5
        self.min_clips = 1
        
        # إعدادات الجودة
        self.min_excitement_score = 0.3
        self.preferred_duration = 25.0  # 25 ثانية مثالية
        
        # إحصائيات
        self.generation_stats = {
            'total_generated': 0,
            'successful_generations': 0,
            'average_excitement': 0.0
        }
        
        logger.info("✅ تم تهيئة مولد الشورتس المبسط")

    def generate_exciting_shorts(self, video_path: str, target_clips: int = None, 
                                output_dir: str = None) -> Dict[str, Any]:
        """إنتاج مقاطع شورتس مثيرة"""
        try:
            start_time = time.time()
            
            # التحقق من الملف
            if not os.path.exists(video_path):
                return self._create_error_result(f"الملف غير موجود: {video_path}")
            
            # تحديد عدد المقاطع المطلوبة
            if target_clips is None:
                target_clips = self.default_target_clips
            
            target_clips = max(self.min_clips, min(target_clips, self.max_clips))
            
            logger.info(f"🎬 بدء إنتاج {target_clips} مقطع شورتس من: {video_path}")
            
            # كشف اللحظات المثيرة
            exciting_moments = self.highlight_detector.detect_exciting_moments(video_path, target_clips)
            
            if not exciting_moments:
                logger.warning("⚠️ لم يتم العثور على لحظات مثيرة")
                return self._create_fallback_clips(video_path, target_clips)
            
            # تحويل إلى تنسيق النتائج
            clips_data = self._convert_to_clips_format(exciting_moments)
            
            # إنشاء معلومات الفيديو
            video_info = self._get_video_info(video_path)
            
            # حساب الإحصائيات
            stats = self._calculate_generation_stats(clips_data)
            
            # تحديث إحصائيات النظام
            self._update_system_stats(clips_data)
            
            processing_time = time.time() - start_time
            
            result = {
                'success': True,
                'video_path': video_path,
                'video_info': video_info,
                'clips': clips_data,
                'clips_count': len(clips_data),
                'target_clips': target_clips,
                'processing_time': processing_time,
                'stats': stats,
                'method': 'simple_excitement_detection',
                'recommendations': self._generate_recommendations(clips_data, stats)
            }
            
            logger.info(f"✅ تم إنتاج {len(clips_data)} مقطع في {processing_time:.1f}s")
            return result
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنتاج الشورتس: {e}")
            return self._create_error_result(str(e))

    def _convert_to_clips_format(self, highlights: List) -> List[Dict[str, Any]]:
        """تحويل اللحظات المثيرة إلى تنسيق المقاطع"""
        try:
            clips = []
            
            for i, highlight in enumerate(highlights):
                clip = {
                    'id': i + 1,
                    'start_time': highlight.start_time,
                    'end_time': highlight.end_time,
                    'duration': highlight.end_time - highlight.start_time,
                    'peak_time': highlight.peak_time,
                    'excitement_score': highlight.excitement_score,
                    'confidence': highlight.excitement_score,  # للتوافق
                    'type': highlight.highlight_type,
                    'description': highlight.description,
                    'reasons': highlight.reasons,
                    'method': 'excitement_detection',
                    'quality': self._assess_clip_quality(highlight),
                    'recommended': highlight.excitement_score > 0.6,
                    'title_suggestion': self._suggest_title(highlight),
                    'tags': self._generate_tags(highlight)
                }
                clips.append(clip)
            
            return clips
            
        except Exception as e:
            logger.error(f"خطأ في تحويل التنسيق: {e}")
            return []

    def _assess_clip_quality(self, highlight) -> str:
        """تقييم جودة المقطع"""
        try:
            score = highlight.excitement_score
            
            if score >= 0.8:
                return "ممتاز"
            elif score >= 0.6:
                return "جيد جداً"
            elif score >= 0.4:
                return "جيد"
            else:
                return "مقبول"
                
        except:
            return "غير محدد"

    def _suggest_title(self, highlight) -> str:
        """اقتراح عنوان للمقطع"""
        try:
            type_titles = {
                'high_action': 'لحظة حماسية مذهلة! 🔥',
                'scene_change': 'تغيير مشهد مثير! ⚡',
                'bright_moment': 'لحظة مضيئة رائعة! ✨',
                'fast_motion': 'حركة سريعة مثيرة! 🚀',
                'dramatic_change': 'تغيير دراماتيكي! 🎭',
                'peak_moment': 'ذروة مثيرة! 🎯',
                'funny_moment': 'لحظة مضحكة! 😂',
                'surprise_moment': 'مفاجأة مذهلة! 😱'
            }
            
            base_title = type_titles.get(highlight.highlight_type, 'لحظة مثيرة! 🎬')
            
            # إضافة وصف الجودة
            if highlight.excitement_score > 0.8:
                return f"🌟 {base_title}"
            elif highlight.excitement_score > 0.6:
                return f"🔥 {base_title}"
            else:
                return base_title
                
        except:
            return "مقطع مثير! 🎬"

    def _generate_tags(self, highlight) -> List[str]:
        """إنشاء علامات للمقطع"""
        try:
            tags = ['شورتس', 'مثير', 'فيديو']
            
            # علامات حسب النوع
            type_tags = {
                'high_action': ['حركة', 'أكشن', 'نشاط'],
                'scene_change': ['تغيير', 'مشهد', 'انتقال'],
                'bright_moment': ['مضيء', 'جميل', 'رائع'],
                'fast_motion': ['سريع', 'حركة', 'ديناميكي'],
                'dramatic_change': ['دراما', 'تغيير', 'مثير'],
                'peak_moment': ['ذروة', 'أفضل', 'مميز'],
                'funny_moment': ['مضحك', 'كوميدي', 'ترفيه'],
                'surprise_moment': ['مفاجأة', 'صادم', 'غير متوقع']
            }
            
            if highlight.highlight_type in type_tags:
                tags.extend(type_tags[highlight.highlight_type])
            
            # علامات حسب الجودة
            if highlight.excitement_score > 0.8:
                tags.extend(['ممتاز', 'عالي_الجودة', 'مذهل'])
            elif highlight.excitement_score > 0.6:
                tags.extend(['جيد', 'مميز'])
            
            return tags[:8]  # أقصى 8 علامات
            
        except:
            return ['شورتس', 'مثير']

    def _get_video_info(self, video_path: str) -> Dict[str, Any]:
        """الحصول على معلومات الفيديو"""
        try:
            cap = cv2.VideoCapture(video_path)
            
            if not cap.isOpened():
                return {}
            
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            duration = total_frames / fps if fps > 0 else 0
            
            cap.release()
            
            # حجم الملف
            file_size = os.path.getsize(video_path)
            
            return {
                'duration': duration,
                'fps': fps,
                'total_frames': total_frames,
                'width': width,
                'height': height,
                'resolution': f"{width}x{height}",
                'file_size': file_size,
                'file_size_mb': file_size / (1024 * 1024),
                'aspect_ratio': width / height if height > 0 else 1.0
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات الفيديو: {e}")
            return {}

    def _calculate_generation_stats(self, clips: List[Dict[str, Any]]) -> Dict[str, Any]:
        """حساب إحصائيات الإنتاج"""
        try:
            if not clips:
                return {}
            
            excitement_scores = [clip['excitement_score'] for clip in clips]
            durations = [clip['duration'] for clip in clips]
            
            stats = {
                'clips_count': len(clips),
                'avg_excitement': sum(excitement_scores) / len(excitement_scores),
                'max_excitement': max(excitement_scores),
                'min_excitement': min(excitement_scores),
                'avg_duration': sum(durations) / len(durations),
                'total_duration': sum(durations),
                'recommended_clips': sum(1 for clip in clips if clip.get('recommended', False)),
                'quality_distribution': {
                    'ممتاز': sum(1 for clip in clips if clip.get('quality') == 'ممتاز'),
                    'جيد جداً': sum(1 for clip in clips if clip.get('quality') == 'جيد جداً'),
                    'جيد': sum(1 for clip in clips if clip.get('quality') == 'جيد'),
                    'مقبول': sum(1 for clip in clips if clip.get('quality') == 'مقبول')
                }
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"خطأ في حساب الإحصائيات: {e}")
            return {}

    def _generate_recommendations(self, clips: List[Dict[str, Any]], stats: Dict[str, Any]) -> List[str]:
        """إنشاء توصيات للمستخدم"""
        try:
            recommendations = []
            
            if not clips:
                recommendations.append("❌ لم يتم العثور على مقاطع مثيرة - جرب فيديو آخر")
                return recommendations
            
            avg_excitement = stats.get('avg_excitement', 0)
            
            # توصيات حسب الجودة العامة
            if avg_excitement > 0.7:
                recommendations.append("🌟 مقاطع ممتازة! جاهزة للنشر فوراً")
            elif avg_excitement > 0.5:
                recommendations.append("✅ مقاطع جيدة - يمكن إضافة تأثيرات بسيطة")
            else:
                recommendations.append("⚠️ مقاطع مقبولة - فكر في تحسينها أو البحث عن فيديو أفضل")
            
            # توصيات حسب عدد المقاطع
            recommended_count = stats.get('recommended_clips', 0)
            if recommended_count > 0:
                recommendations.append(f"🎯 {recommended_count} مقطع موصى به للنشر")
            
            # توصيات حسب المدة
            avg_duration = stats.get('avg_duration', 0)
            if avg_duration > 40:
                recommendations.append("⏱️ المقاطع طويلة نسبياً - فكر في تقصيرها")
            elif avg_duration < 15:
                recommendations.append("⏱️ المقاطع قصيرة - فكر في إطالتها قليلاً")
            
            # توصيات حسب التنوع
            quality_dist = stats.get('quality_distribution', {})
            excellent_count = quality_dist.get('ممتاز', 0)
            if excellent_count > 0:
                recommendations.append(f"💎 {excellent_count} مقطع ممتاز - ابدأ بهذه")
            
            # توصية عامة
            recommendations.append("💡 نصيحة: أضف موسيقى وتأثيرات لزيادة الجاذبية")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء التوصيات: {e}")
            return ["تم إنتاج المقاطع بنجاح"]

    def _create_fallback_clips(self, video_path: str, target_clips: int) -> Dict[str, Any]:
        """إنشاء مقاطع احتياطية عند فشل الكشف"""
        try:
            logger.info("🔄 إنشاء مقاطع احتياطية...")
            
            video_info = self._get_video_info(video_path)
            duration = video_info.get('duration', 60)
            
            # إنشاء مقاطع موزعة بالتساوي
            clips = []
            clip_duration = min(25.0, duration / target_clips)
            
            for i in range(target_clips):
                start_time = (duration / target_clips) * i
                end_time = min(start_time + clip_duration, duration)
                
                if end_time - start_time < 10:  # تجاهل المقاطع القصيرة جداً
                    continue
                
                clip = {
                    'id': i + 1,
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration': end_time - start_time,
                    'peak_time': (start_time + end_time) / 2,
                    'excitement_score': 0.4,  # نقاط متوسطة
                    'confidence': 0.4,
                    'type': 'fallback_clip',
                    'description': f'مقطع تلقائي {i + 1}',
                    'reasons': ['مقطع احتياطي'],
                    'method': 'fallback_generation',
                    'quality': 'مقبول',
                    'recommended': False,
                    'title_suggestion': f'مقطع {i + 1} 🎬',
                    'tags': ['شورتس', 'تلقائي']
                }
                clips.append(clip)
            
            stats = self._calculate_generation_stats(clips)
            
            return {
                'success': True,
                'video_path': video_path,
                'video_info': video_info,
                'clips': clips,
                'clips_count': len(clips),
                'target_clips': target_clips,
                'processing_time': 1.0,
                'stats': stats,
                'method': 'fallback_generation',
                'recommendations': [
                    "⚠️ تم إنشاء مقاطع احتياطية",
                    "💡 جرب فيديو أكثر حيوية للحصول على نتائج أفضل"
                ]
            }
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء المقاطع الاحتياطية: {e}")
            return self._create_error_result("فشل في إنشاء مقاطع احتياطية")

    def _update_system_stats(self, clips: List[Dict[str, Any]]):
        """تحديث إحصائيات النظام"""
        try:
            self.generation_stats['total_generated'] += 1
            
            if clips:
                self.generation_stats['successful_generations'] += 1
                
                # تحديث متوسط الإثارة
                avg_excitement = sum(clip['excitement_score'] for clip in clips) / len(clips)
                current_avg = self.generation_stats['average_excitement']
                total_gens = self.generation_stats['total_generated']
                
                new_avg = ((current_avg * (total_gens - 1)) + avg_excitement) / total_gens
                self.generation_stats['average_excitement'] = new_avg
                
        except Exception as e:
            logger.error(f"خطأ في تحديث إحصائيات النظام: {e}")

    def _create_error_result(self, error_message: str) -> Dict[str, Any]:
        """إنشاء نتيجة خطأ"""
        return {
            'success': False,
            'error': error_message,
            'clips': [],
            'clips_count': 0,
            'recommendations': [f"❌ خطأ: {error_message}"]
        }

    def get_system_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات النظام"""
        return {
            'generator_stats': self.generation_stats,
            'settings': {
                'default_target_clips': self.default_target_clips,
                'max_clips': self.max_clips,
                'min_excitement_score': self.min_excitement_score,
                'preferred_duration': self.preferred_duration
            },
            'detector_info': {
                'min_clip_duration': self.highlight_detector.min_clip_duration,
                'max_clip_duration': self.highlight_detector.max_clip_duration,
                'optimal_duration': self.highlight_detector.optimal_duration
            }
        }

# دالة مبسطة للاستخدام المباشر
def generate_simple_shorts(video_path: str, target_clips: int = 3) -> Dict[str, Any]:
    """إنتاج شورتس مبسط - دالة سريعة"""
    try:
        generator = SimpleShortsGenerator()
        return generator.generate_exciting_shorts(video_path, target_clips)
    except Exception as e:
        logger.error(f"خطأ في إنتاج الشورتس المبسط: {e}")
        return {'success': False, 'error': str(e), 'clips': []}

if __name__ == "__main__":
    # اختبار النظام
    print("🧪 اختبار مولد الشورتس المبسط")
    print("=" * 50)
    
    generator = SimpleShortsGenerator()
    print("✅ تم تهيئة المولد")
    
    stats = generator.get_system_stats()
    print(f"📊 إحصائيات النظام:")
    print(f"   - المقاطع المولدة: {stats['generator_stats']['total_generated']}")
    print(f"   - المولدات الناجحة: {stats['generator_stats']['successful_generations']}")
    print(f"   - متوسط الإثارة: {stats['generator_stats']['average_excitement']:.2f}")
    
    print("\n🏁 انتهى الاختبار")
