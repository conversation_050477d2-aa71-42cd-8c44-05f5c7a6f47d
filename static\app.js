// AI Youtube Shorts Generator - JavaScript
let statusCheckInterval;
let currentVideoId = null;

// معالجة الأخطاء العامة
window.addEventListener('error', function(e) {
    console.error('خطأ JavaScript:', e.error);
});

// معالجة الأخطاء غير المعالجة في Promise
window.addEventListener('unhandledrejection', function(e) {
    console.error('خطأ Promise غير معالج:', e.reason);
    e.preventDefault();
});

// دالة مساعدة لمعالجة fetch requests
async function safeFetch(url, options = {}) {
    try {
        const response = await fetch(url, options);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return await response.json();
        } else {
            return await response.text();
        }
    } catch (error) {
        console.error(`خطأ في الطلب إلى ${url}:`, error);
        throw error;
    }
}

// تحديث حالة النظام
async function updateStatus() {
    try {
        const data = await safeFetch('/api/status');

        const statusCard = document.getElementById('statusCard');
        const statusMessage = document.getElementById('statusMessage');

        if (data.requirements) {
            statusCard.className = 'status-card';
            statusCard.style.borderLeftColor = '#28a745';
            statusMessage.textContent = data.message;
            statusCard.querySelector('i').className = 'fas fa-check-circle text-success me-3 fs-4';
        } else {
            statusCard.style.borderLeftColor = '#dc3545';
            statusMessage.textContent = data.message;
            statusCard.querySelector('i').className = 'fas fa-exclamation-circle text-danger me-3 fs-4';
        }

        // تحديث شريط التقدم إذا كان هناك معالجة جارية
        if (data.current_status.status !== 'ready' && data.current_status.status !== 'completed') {
            updateProgress(data.current_status);
        } else if (data.current_status.status === 'completed') {
            hideProgress();
            loadVideos();
            showNotification('تم إنجاز المعالجة بنجاح!', 'success');
        }
    } catch (error) {
        console.error('خطأ في تحديث الحالة:', error);
        // عرض رسالة خطأ للمستخدم
        const statusMessage = document.getElementById('statusMessage');
        if (statusMessage) {
            statusMessage.textContent = 'خطأ في الاتصال بالخادم';
        }
    }
}

// معالجة فيديو
function processVideo() {
    const url = document.getElementById('youtubeUrl').value.trim();
    
    if (!url) {
        showNotification('يرجى إدخال رابط الفيديو!', 'error');
        return;
    }
    
    if (!url.includes('youtube.com') && !url.includes('youtu.be')) {
        showNotification('يرجى إدخال رابط YouTube صحيح!', 'error');
        return;
    }
    
    // إظهار شريط التقدم
    showProgress();
    
    fetch('/api/process_video', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: url })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            // بدء مراقبة التقدم
            startProgressMonitoring();
        } else {
            hideProgress();
            showNotification(data.error, 'error');
        }
    })
    .catch(error => {
        hideProgress();
        showNotification('خطأ في الاتصال بالخادم', 'error');
        console.error('Error:', error);
    });
}

// إظهار شريط التقدم
function showProgress() {
    document.getElementById('progressSection').style.display = 'block';
    document.getElementById('progressBar').style.width = '0%';
    document.getElementById('progressMessage').textContent = 'بدء المعالجة...';
}

// إخفاء شريط التقدم
function hideProgress() {
    document.getElementById('progressSection').style.display = 'none';
}

// تحديث شريط التقدم
function updateProgress(status) {
    const progressBar = document.getElementById('progressBar');
    const progressMessage = document.getElementById('progressMessage');
    
    if (progressBar && progressMessage) {
        progressBar.style.width = status.progress + '%';
        progressMessage.textContent = status.message;
        
        // تغيير لون شريط التقدم حسب الحالة
        progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
        if (status.status === 'error') {
            progressBar.classList.add('bg-danger');
        } else if (status.status === 'completed') {
            progressBar.classList.add('bg-success');
        }
    }
}

// بدء مراقبة التقدم
function startProgressMonitoring() {
    if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
    }
    
    statusCheckInterval = setInterval(() => {
        updateStatus();
    }, 2000); // تحديث كل ثانيتين
}

// تحميل قائمة الفيديوهات
function loadVideos() {
    fetch('/api/videos')
        .then(response => response.json())
        .then(data => {
            const videosList = document.getElementById('videosList');

            if (data.videos.length === 0) {
                videosList.innerHTML = '<p class="text-muted text-center py-5">لا توجد فيديوهات معالجة بعد</p>';
                return;
            }

            let videosHTML = '';
            data.videos.forEach(video => {
                videosHTML += createVideoCard(video);
            });

            videosList.innerHTML = videosHTML;
        })
        .catch(error => {
            console.error('خطأ في تحميل الفيديوهات:', error);
        });
}

// تحميل قائمة المقاطع القصيرة
async function loadShorts() {
    try {
        const data = await safeFetch('/api/shorts');
        const shortsList = document.getElementById('shortsList');

        if (data.shorts.length === 0) {
            shortsList.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-scissors fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مقاطع محفوظة بعد</h5>
                    <p class="text-muted">قم بمعالجة فيديو أولاً، ثم اضغط "إنشاء مقطع" من المقاطع المقترحة</p>
                    <button onclick="document.getElementById('process-tab').click()" class="btn btn-primary">
                        <i class="fas fa-plus"></i> معالجة فيديو جديد
                    </button>
                </div>
            `;
            return;
        }

        let shortsHTML = '';
        data.shorts.forEach(short => {
            shortsHTML += createShortCard(short);
        });

        shortsList.innerHTML = shortsHTML;
    } catch (error) {
        console.error('خطأ في تحميل المقاطع القصيرة:', error);
        const shortsList = document.getElementById('shortsList');
        shortsList.innerHTML = '<p class="text-danger text-center py-5">خطأ في تحميل المقاطع القصيرة</p>';
    }
}

// إنشاء بطاقة فيديو
function createVideoCard(video) {
    return `
        <div class="video-card">
            <div class="row">
                <div class="col-md-8">
                    <h5><i class="fas fa-video"></i> ${video.title}</h5>
                    <p class="text-muted mb-2">
                        <i class="fas fa-clock"></i> ${video.processed_at}
                    </p>
                    <p class="mb-3">${video.transcript.substring(0, 150)}...</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="btn-group-vertical w-100" role="group">
                        <button class="btn btn-outline-primary btn-sm mb-1" onclick="viewVideoDetails(${video.id})">
                            <i class="fas fa-eye"></i> عرض التفاصيل
                        </button>
                        <button class="btn btn-outline-success btn-sm mb-1" onclick="createShorts(${video.id})">
                            <i class="fas fa-cut"></i> إنشاء مقاطع
                        </button>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-info btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-download"></i> تحميل
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/api/download_video/${video.id}" target="_blank">
                                    <i class="fas fa-video"></i> الفيديو الأصلي
                                </a></li>
                                <li><a class="dropdown-item" href="/api/download_audio/${video.id}" target="_blank">
                                    <i class="fas fa-music"></i> الملف الصوتي
                                </a></li>
                                <li><a class="dropdown-item" href="/api/download_transcript/${video.id}" target="_blank">
                                    <i class="fas fa-file-text"></i> النص المنسوخ
                                </a></li>
                            </ul>
                        </div>
                        <button class="btn btn-outline-danger btn-sm mt-1" onclick="deleteVideo(${video.id})">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="mt-3">
                <h6><i class="fas fa-star"></i> المقاطع المقترحة:</h6>
                <div class="row">
                    ${video.highlights.map(highlight => `
                        <div class="col-md-4">
                            <div class="highlight-item">
                                <small class="text-muted">${highlight.start} - ${highlight.end}</small>
                                <p class="mb-2">${highlight.text}</p>
                                <button class="btn btn-sm btn-custom" onclick="createShort(${video.id}, '${highlight.start}', '${highlight.end}')">
                                    <i class="fas fa-scissors"></i> إنشاء مقطع
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;
}

// عرض تفاصيل الفيديو
function viewVideoDetails(videoId) {
    fetch(`/api/video/${videoId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showVideoModal(data.video);
            } else {
                showNotification(data.error, 'error');
            }
        })
        .catch(error => {
            showNotification('خطأ في تحميل تفاصيل الفيديو', 'error');
        });
}

// إنشاء مقطع قصير
function createShort(videoId, startTime, endTime) {
    fetch('/api/create_short', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            video_id: videoId,
            start_time: startTime,
            end_time: endTime
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم إنشاء المقطع القصير بنجاح!', 'success');
        } else {
            showNotification(data.error, 'error');
        }
    })
    .catch(error => {
        showNotification('خطأ في إنشاء المقطع القصير', 'error');
    });
}

// حفظ الإعدادات
function saveSettings() {
    const apiKey = document.getElementById('apiKey').value;
    const outputQuality = document.getElementById('outputQuality').value;
    const autoCrop = document.getElementById('autoCrop').checked;
    
    fetch('/api/settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            api_key: apiKey,
            output_quality: outputQuality,
            auto_crop: autoCrop
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
        } else {
            showNotification('خطأ في حفظ الإعدادات', 'error');
        }
    })
    .catch(error => {
        showNotification('خطأ في الاتصال بالخادم', 'error');
    });
}

// إظهار إشعار
function showNotification(message, type) {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    let alertClass, iconClass;

    switch(type) {
        case 'success':
            alertClass = 'alert-success';
            iconClass = 'fa-check-circle';
            break;
        case 'error':
            alertClass = 'alert-danger';
            iconClass = 'fa-exclamation-circle';
            break;
        case 'info':
            alertClass = 'alert-info';
            iconClass = 'fa-info-circle';
            break;
        default:
            alertClass = 'alert-primary';
            iconClass = 'fa-bell';
    }

    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <i class="fas ${iconClass}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // إزالة الإشعار تلقائياً بعد 5 ثوانٍ
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// تحميل الإعدادات
function loadSettings() {
    fetch('/api/settings')
        .then(response => response.json())
        .then(data => {
            if (data.api_key_set) {
                document.getElementById('apiKey').placeholder = 'مفتاح API محفوظ';
            }
            document.getElementById('outputQuality').value = data.output_quality || 'high';
            document.getElementById('autoCrop').checked = data.auto_crop !== false;
        })
        .catch(error => {
            console.error('خطأ في تحميل الإعدادات:', error);
        });
}

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    // تحديث الحالة عند التحميل
    updateStatus();
    
    // تحميل الفيديوهات
    loadVideos();
    
    // تحميل الإعدادات
    loadSettings();
    
    // تحديث دوري للحالة
    setInterval(updateStatus, 10000); // كل 10 ثوانٍ
    
    // إضافة مستمع لتبديل التبويبات
    document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(e) {
            if (e.target.id === 'videos-tab') {
                loadVideos();
            } else if (e.target.id === 'shorts-tab') {
                loadShorts();
            }
        });
    });
    
    // إضافة مستمع لمفتاح Enter في حقل الرابط
    document.getElementById('youtubeUrl').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            processVideo();
        }
    });

    // إضافة مستمع لمفتاح Enter في حقل البحث
    document.getElementById('searchVideos').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchVideos();
        }
    });
});

// إنشاء بطاقة مقطع قصير
function createShortCard(short) {
    const hasFile = short.file_path && short.status === "تم الإنشاء بنجاح";
    const statusBadge = hasFile ?
        '<span class="badge bg-success"><i class="fas fa-check"></i> جاهز للتحميل</span>' :
        '<span class="badge bg-warning"><i class="fas fa-clock"></i> يحتاج إنشاء ملف</span>';

    return `
        <div class="video-card">
            <div class="row">
                <div class="col-md-8">
                    <h6><i class="fas fa-scissors"></i> ${short.title}</h6>
                    <p class="text-muted mb-1">
                        <i class="fas fa-video"></i> من الفيديو: ${short.video_title}
                    </p>
                    <p class="text-muted mb-2">
                        <i class="fas fa-clock"></i> ${short.start_time} - ${short.end_time}
                        <span class="ms-3"><i class="fas fa-calendar"></i> ${new Date(short.created_at).toLocaleDateString('ar')}</span>
                    </p>
                    <p class="mb-0">${statusBadge}</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-grid gap-2">
                        ${hasFile ? `
                            <button class="btn btn-success btn-sm" onclick="downloadShort(${short.id})">
                                <i class="fas fa-download"></i> تحميل الملف
                            </button>
                        ` : `
                            <button class="btn btn-primary btn-sm" onclick="generateShortFile(${short.id})">
                                <i class="fas fa-cog"></i> إنشاء ملف MP4
                            </button>
                        `}
                        <button class="btn btn-outline-info btn-sm" onclick="viewShortDetails(${short.id})">
                            <i class="fas fa-eye"></i> التفاصيل
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="deleteShort(${short.id})">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// البحث في الفيديوهات
function searchVideos() {
    const query = document.getElementById('searchVideos').value.trim();

    if (!query) {
        loadVideos();
        return;
    }

    fetch(`/api/search_videos?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            const videosList = document.getElementById('videosList');

            if (data.videos.length === 0) {
                videosList.innerHTML = '<p class="text-muted text-center py-5">لا توجد نتائج للبحث</p>';
                return;
            }

            let videosHTML = '';
            data.videos.forEach(video => {
                videosHTML += createVideoCard(video);
            });

            videosList.innerHTML = videosHTML;
        })
        .catch(error => {
            console.error('خطأ في البحث:', error);
            showNotification('خطأ في البحث', 'error');
        });
}

// حذف فيديو
function deleteVideo(videoId) {
    if (!confirm('هل أنت متأكد من حذف هذا الفيديو؟ سيتم حذف جميع المقاطع المرتبطة به أيضاً.')) {
        return;
    }

    fetch(`/api/delete_video/${videoId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            loadVideos();
            loadShorts();
        } else {
            showNotification(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('خطأ في حذف الفيديو:', error);
        showNotification('خطأ في حذف الفيديو', 'error');
    });
}

// تصدير البيانات
function exportData() {
    fetch('/api/export_data')
        .then(response => response.json())
        .then(data => {
            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `ai_shorts_data_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showNotification('تم تصدير البيانات بنجاح', 'success');
        })
        .catch(error => {
            console.error('خطأ في تصدير البيانات:', error);
            showNotification('خطأ في تصدير البيانات', 'error');
        });
}

// تحميل الإحصائيات
function loadStatistics() {
    fetch('/api/statistics')
        .then(response => response.json())
        .then(data => {
            const statsHTML = `
                <div class="modal fade" id="statsModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="fas fa-chart-bar"></i> إحصائيات النظام</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row text-center">
                                    <div class="col-md-6">
                                        <div class="card bg-primary text-white mb-3">
                                            <div class="card-body">
                                                <h3>${data.total_videos}</h3>
                                                <p>إجمالي الفيديوهات</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card bg-success text-white mb-3">
                                            <div class="card-body">
                                                <h3>${data.total_shorts}</h3>
                                                <p>إجمالي المقاطع القصيرة</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <p class="text-muted">آخر تحديث: ${new Date(data.last_updated).toLocaleString('ar')}</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // إزالة النافذة المنبثقة السابقة إن وجدت
            const existingModal = document.getElementById('statsModal');
            if (existingModal) {
                existingModal.remove();
            }

            // إضافة النافذة الجديدة
            document.body.insertAdjacentHTML('beforeend', statsHTML);

            // إظهار النافذة
            const modal = new bootstrap.Modal(document.getElementById('statsModal'));
            modal.show();
        })
        .catch(error => {
            console.error('خطأ في تحميل الإحصائيات:', error);
            showNotification('خطأ في تحميل الإحصائيات', 'error');
        });
}

// فحص APIs
function checkAPIs() {
    showNotification('جاري فحص APIs...', 'info');

    fetch('/api/check_apis')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAPIResults(data.results, data.status);
            } else {
                showNotification(data.error, 'error');
            }
        })
        .catch(error => {
            console.error('خطأ في فحص APIs:', error);
            showNotification('خطأ في فحص APIs', 'error');
        });
}

// عرض نتائج فحص APIs
function showAPIResults(results, status) {
    const getStatusIcon = (isOk) => isOk ? '✅' : '❌';
    const getStatusClass = (isOk) => isOk ? 'text-success' : 'text-danger';

    const apiResultsHTML = `
        <div class="modal fade" id="apiModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="fas fa-wifi"></i> نتائج فحص APIs</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-${status.all_required_ok ? 'success' : 'warning'}" role="alert">
                            <h6>${status.all_required_ok ? '🎉 جميع APIs المطلوبة تعمل بشكل صحيح!' : '⚠️ بعض APIs لا تعمل'}</h6>
                        </div>

                        <div class="row">
                            ${Object.entries(results).map(([apiName, result]) => `
                                <div class="col-md-6 mb-3">
                                    <div class="card ${result.status ? 'border-success' : 'border-danger'}">
                                        <div class="card-body">
                                            <h6 class="card-title ${getStatusClass(result.status)}">
                                                ${getStatusIcon(result.status)} ${apiName.toUpperCase()}
                                                ${result.required ? '<span class="badge bg-primary ms-2">مطلوب</span>' : '<span class="badge bg-secondary ms-2">اختياري</span>'}
                                            </h6>
                                            <p class="card-text">${result.message}</p>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>

                        ${!status.ai_available ? `
                            <div class="alert alert-info" role="alert">
                                <h6><i class="fas fa-info-circle"></i> ملاحظة حول APIs الذكاء الاصطناعي:</h6>
                                <ul class="mb-0">
                                    <li>الأداة ستعمل بدون AI APIs ولكن بميزات محدودة</li>
                                    <li>للحصول على الميزات الكاملة، احصل على أحد المفاتيح التالية:</li>
                                    <li><strong>Gemini API (مجاني):</strong> <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a></li>
                                    <li><strong>OpenAI API:</strong> <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI Platform</a></li>
                                    <li>ضع المفتاح في ملف .env:</li>
                                    <li><code>GEMINI_API_KEY=your-gemini-key-here</code></li>
                                    <li><code>OPENAI_API=sk-your-key-here</code></li>
                                </ul>
                            </div>
                        ` : ''}

                        ${status.gemini_ok ? `
                            <div class="alert alert-success" role="alert">
                                <h6><i class="fas fa-star"></i> ✨ Gemini AI متاح!</h6>
                                <p class="mb-0">ستحصل على تحليل ذكي متقدم للفيديوهات باستخدام Gemini من Google</p>
                            </div>
                        ` : ''}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        <button type="button" class="btn btn-primary" onclick="checkAPIs()">إعادة الفحص</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إزالة النافذة المنبثقة السابقة إن وجدت
    const existingModal = document.getElementById('apiModal');
    if (existingModal) {
        existingModal.remove();
    }

    // إضافة النافذة الجديدة
    document.body.insertAdjacentHTML('beforeend', apiResultsHTML);

    // إظهار النافذة
    const modal = new bootstrap.Modal(document.getElementById('apiModal'));
    modal.show();
}

// تحميل مقطع قصير
function downloadShort(shortId) {
    // إنشاء رابط تحميل مخفي
    const link = document.createElement('a');
    link.href = `/api/download_short/${shortId}`;
    link.download = '';
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showNotification('بدء تحميل المقطع القصير...', 'success');
}

// إنشاء ملف MP4 للمقطع القصير
async function generateShortFile(shortId) {
    try {
        showNotification('جاري إنشاء ملف MP4...', 'info');

        const response = await safeFetch(`/api/generate_short_file/${shortId}`, {
            method: 'POST'
        });

        if (response.success) {
            showNotification('تم إنشاء الملف بنجاح!', 'success');
            // إعادة تحميل قائمة المقاطع لإظهار التحديث
            loadShorts();
        } else {
            showNotification(response.error || 'فشل في إنشاء الملف', 'error');
        }
    } catch (error) {
        console.error('خطأ في إنشاء الملف:', error);
        showNotification('خطأ في إنشاء الملف', 'error');
    }
}

// حذف مقطع قصير
async function deleteShort(shortId) {
    if (!confirm('هل أنت متأكد من حذف هذا المقطع؟')) {
        return;
    }

    try {
        const response = await safeFetch(`/api/delete_short/${shortId}`, {
            method: 'DELETE'
        });

        if (response.success) {
            showNotification('تم حذف المقطع بنجاح', 'success');
            loadShorts();
        } else {
            showNotification(response.error || 'فشل في حذف المقطع', 'error');
        }
    } catch (error) {
        console.error('خطأ في حذف المقطع:', error);
        showNotification('خطأ في حذف المقطع', 'error');
    }
}

// عرض تفاصيل مقطع قصير
function viewShortDetails(shortId) {
    fetch(`/api/shorts`)
        .then(response => response.json())
        .then(data => {
            const short = data.shorts.find(s => s.id === shortId);
            if (short) {
                const detailsHTML = `
                    <div class="modal fade" id="shortDetailsModal" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title"><i class="fas fa-scissors"></i> تفاصيل المقطع القصير</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <strong>العنوان:</strong><br>
                                            ${short.title}
                                        </div>
                                        <div class="col-md-6">
                                            <strong>الفيديو الأصلي:</strong><br>
                                            ${short.video_title}
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <strong>وقت البداية:</strong><br>
                                            ${short.start_time}
                                        </div>
                                        <div class="col-md-6">
                                            <strong>وقت النهاية:</strong><br>
                                            ${short.end_time}
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-12">
                                            <strong>تاريخ الإنشاء:</strong><br>
                                            ${new Date(short.created_at).toLocaleString('ar')}
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-12">
                                            <strong>الحالة:</strong><br>
                                            <span class="badge bg-success">${short.status}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                    <button type="button" class="btn btn-primary" onclick="downloadShort(${short.id})">
                                        <i class="fas fa-download"></i> تحميل
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // إزالة النافذة السابقة إن وجدت
                const existingModal = document.getElementById('shortDetailsModal');
                if (existingModal) {
                    existingModal.remove();
                }

                // إضافة النافذة الجديدة
                document.body.insertAdjacentHTML('beforeend', detailsHTML);

                // إظهار النافذة
                const modal = new bootstrap.Modal(document.getElementById('shortDetailsModal'));
                modal.show();
            } else {
                showNotification('المقطع القصير غير موجود', 'error');
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل تفاصيل المقطع:', error);
            showNotification('خطأ في تحميل تفاصيل المقطع', 'error');
        });
}
