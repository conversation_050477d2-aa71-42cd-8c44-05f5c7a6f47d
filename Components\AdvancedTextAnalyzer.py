#!/usr/bin/env python3
"""
محلل النصوص المتقدم
Advanced Text Analysis System

يستخدم تقنيات متقدمة لتحليل النصوص وكشف المشاعر والأنماط الدلالية
مدمج من الأداة المتقدمة مع تحسينات للأداة الحالية
"""

import re
import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
import json
import statistics
from collections import Counter, defaultdict

# محاولة استيراد المكتبات المتقدمة
try:
    from textblob import TextBlob
    TEXTBLOB_AVAILABLE = True
except ImportError:
    TEXTBLOB_AVAILABLE = False
    TextBlob = None

try:
    import nltk
    from nltk.sentiment import SentimentIntensityAnalyzer
    NLTK_AVAILABLE = True
    # تحميل البيانات المطلوبة
    try:
        nltk.data.find('vader_lexicon')
    except LookupError:
        nltk.download('vader_lexicon', quiet=True)
except ImportError:
    NLTK_AVAILABLE = False
    nltk = None

logger = logging.getLogger(__name__)

@dataclass
class TextFeatures:
    """خصائص نصية مستخرجة"""
    timestamp: float
    text: str
    sentiment_score: float
    emotion_scores: Dict[str, float]
    keywords: List[str]
    viral_keywords: List[str]
    readability_score: float
    urgency_score: float
    engagement_potential: float
    language_detected: str
    text_length: int
    word_count: int

@dataclass
class TextHighlight:
    """لحظة نصية مميزة"""
    start_time: float
    end_time: float
    text_segment: str
    highlight_type: str
    confidence: float
    features: TextFeatures
    viral_potential: float
    emotional_impact: float

class AdvancedTextAnalyzer:
    """محلل النصوص المتقدم"""
    
    def __init__(self):
        # إعداد محللات المشاعر
        if NLTK_AVAILABLE:
            try:
                self.vader_analyzer = SentimentIntensityAnalyzer()
                logger.info("تم تهيئة VADER sentiment analyzer")
            except Exception as e:
                logger.warning(f"فشل في تهيئة VADER: {e}")
                self.vader_analyzer = None
        else:
            self.vader_analyzer = None
        
        # كلمات مفتاحية محسنة للمحتوى الفيروسي
        self.viral_keywords = {
            'arabic': {
                'excitement': ['مذهل', 'رائع', 'لا أصدق', 'واو', 'يا إلهي', 'مستحيل', 'خرافي', 'أسطوري'],
                'urgency': ['عاجل', 'حصري', 'أول مرة', 'كسر', 'فضيحة', 'سر', 'مفاجأة'],
                'achievement': ['إنجاز', 'قياسي', 'تاريخي', 'نادر', 'فريد', 'استثنائي', 'بطولة'],
                'emotion': ['صدمة', 'فرح', 'حزن', 'غضب', 'خوف', 'دهشة', 'إعجاب'],
                'social': ['فيروسي', 'ترند', 'شائع', 'منتشر', 'مشهور', 'محبوب'],
                'superlatives': ['الأفضل', 'الأسوأ', 'الأكبر', 'الأصغر', 'الأقوى', 'الأسرع']
            },
            'english': {
                'excitement': ['amazing', 'incredible', 'unbelievable', 'wow', 'omg', 'insane', 'epic', 'legendary'],
                'urgency': ['breaking', 'exclusive', 'first time', 'leaked', 'scandal', 'secret', 'surprise'],
                'achievement': ['record', 'historic', 'rare', 'unique', 'extraordinary', 'championship', 'victory'],
                'emotion': ['shocking', 'heartwarming', 'devastating', 'hilarious', 'terrifying', 'stunning'],
                'social': ['viral', 'trending', 'popular', 'famous', 'beloved', 'controversial'],
                'superlatives': ['best', 'worst', 'biggest', 'smallest', 'strongest', 'fastest', 'ultimate']
            }
        }
        
        # أنماط نصية للكشف
        self.text_patterns = {
            'question': r'[؟?]',
            'exclamation': r'[!！]',
            'numbers': r'\d+',
            'hashtags': r'#\w+',
            'mentions': r'@\w+',
            'urls': r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+',
            'caps_words': r'\b[A-Z]{2,}\b',
            'repeated_chars': r'(.)\1{2,}',
            'emojis': r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF]'
        }
        
        # أنواع اللحظات النصية
        self.text_highlight_types = {
            'viral_phrase': 'عبارة فيروسية',
            'emotional_peak': 'ذروة عاطفية',
            'call_to_action': 'دعوة للعمل',
            'surprising_fact': 'حقيقة مفاجئة',
            'funny_moment': 'لحظة مضحكة',
            'dramatic_statement': 'تصريح دراماتيكي',
            'educational_insight': 'نظرة تعليمية',
            'controversial_opinion': 'رأي مثير للجدل'
        }
        
        # قواميس المشاعر العربية
        self.arabic_sentiment_words = {
            'positive': ['سعيد', 'فرح', 'رائع', 'جميل', 'ممتاز', 'مذهل', 'حب', 'إعجاب', 'نجاح', 'فوز'],
            'negative': ['حزين', 'غضب', 'سيء', 'فظيع', 'مروع', 'كره', 'فشل', 'خسارة', 'ألم', 'صعب'],
            'neutral': ['عادي', 'طبيعي', 'متوسط', 'مقبول', 'لا بأس', 'ممكن', 'ربما', 'أحياناً']
        }
        
        logger.info("تم تهيئة محلل النصوص المتقدم")
        logger.info(f"TextBlob متوفر: {'نعم' if TEXTBLOB_AVAILABLE else 'لا'}")
        logger.info(f"NLTK متوفر: {'نعم' if NLTK_AVAILABLE else 'لا'}")

    def analyze_text_comprehensive(self, text: str, timestamps: List[float] = None) -> Dict[str, Any]:
        """تحليل نص شامل"""
        try:
            if not text or not text.strip():
                return self._get_empty_analysis()
            
            # تقسيم النص إلى جمل مع الأوقات
            sentences = self._split_text_with_timestamps(text, timestamps)
            
            # تحليل شامل
            global_features = self._extract_global_text_features(text)
            sentence_features = self._extract_sentence_features(sentences)
            highlights = self._detect_text_highlights(sentence_features)
            
            # تحليل متقدم للمشاعر
            emotion_analysis = self._analyze_emotions_advanced(text, sentence_features)
            
            # كشف أنماط خاصة
            special_patterns = self._detect_special_text_patterns(text)
            
            # تحليل الفيروسية
            viral_analysis = self._analyze_viral_potential(text, sentence_features)
            
            return {
                'text_length': len(text),
                'word_count': len(text.split()),
                'sentence_count': len(sentences),
                'global_features': global_features,
                'sentence_features': sentence_features,
                'highlights': highlights,
                'emotion_analysis': emotion_analysis,
                'special_patterns': special_patterns,
                'viral_analysis': viral_analysis,
                'readability_metrics': self._calculate_readability(text),
                'language_detection': self._detect_language(text)
            }
            
        except Exception as e:
            logger.error(f"خطأ في التحليل الشامل للنص: {e}")
            return self._get_empty_analysis()

    def _split_text_with_timestamps(self, text: str, timestamps: List[float] = None) -> List[Dict[str, Any]]:
        """تقسيم النص إلى جمل مع الأوقات"""
        try:
            # تقسيم بناءً على علامات الترقيم
            sentence_endings = r'[.!?؟!。]'
            sentences = re.split(sentence_endings, text)
            sentences = [s.strip() for s in sentences if s.strip()]
            
            # ربط الجمل بالأوقات
            sentence_data = []
            total_sentences = len(sentences)
            
            for i, sentence in enumerate(sentences):
                # تقدير الوقت بناءً على موضع الجملة
                if timestamps and len(timestamps) >= 2:
                    start_time = timestamps[0]
                    end_time = timestamps[-1]
                    duration = end_time - start_time
                    
                    # توزيع الجمل على المدة الزمنية
                    sentence_start = start_time + (i / total_sentences) * duration
                    sentence_end = start_time + ((i + 1) / total_sentences) * duration
                else:
                    # أوقات افتراضية (5 ثوانٍ لكل جملة)
                    sentence_start = i * 5.0
                    sentence_end = (i + 1) * 5.0
                
                sentence_data.append({
                    'text': sentence,
                    'start_time': sentence_start,
                    'end_time': sentence_end,
                    'index': i
                })
            
            return sentence_data
            
        except Exception as e:
            logger.error(f"خطأ في تقسيم النص: {e}")
            return [{'text': text, 'start_time': 0.0, 'end_time': 10.0, 'index': 0}]

    def _extract_global_text_features(self, text: str) -> Dict[str, Any]:
        """استخراج الخصائص النصية العامة"""
        try:
            features = {}
            
            # إحصائيات أساسية
            words = text.split()
            features['total_words'] = len(words)
            features['unique_words'] = len(set(words))
            features['avg_word_length'] = statistics.mean([len(word) for word in words]) if words else 0
            
            # كثافة علامات الترقيم
            features['exclamation_density'] = len(re.findall(r'[!！]', text)) / max(len(text), 1)
            features['question_density'] = len(re.findall(r'[؟?]', text)) / max(len(text), 1)
            
            # كثافة الكلمات الكبيرة
            caps_words = re.findall(r'\b[A-Z]{2,}\b', text)
            features['caps_density'] = len(caps_words) / max(len(words), 1)
            
            # كثافة الأرقام
            numbers = re.findall(r'\d+', text)
            features['number_density'] = len(numbers) / max(len(words), 1)
            
            # المشاعر العامة
            if TEXTBLOB_AVAILABLE:
                blob = TextBlob(text)
                features['textblob_sentiment'] = blob.sentiment.polarity
                features['textblob_subjectivity'] = blob.sentiment.subjectivity
            else:
                features['textblob_sentiment'] = self._basic_sentiment_analysis(text)
                features['textblob_subjectivity'] = 0.5
            
            # VADER sentiment (إذا كان متوفراً)
            if self.vader_analyzer:
                vader_scores = self.vader_analyzer.polarity_scores(text)
                features['vader_compound'] = vader_scores['compound']
                features['vader_positive'] = vader_scores['pos']
                features['vader_negative'] = vader_scores['neg']
                features['vader_neutral'] = vader_scores['neu']
            
            # كثافة الكلمات الفيروسية
            viral_count = self._count_viral_keywords(text)
            features['viral_keyword_density'] = viral_count / max(len(words), 1)
            
            return features
            
        except Exception as e:
            logger.error(f"خطأ في استخراج الخصائص العامة: {e}")
            return {}

    def _extract_sentence_features(self, sentences: List[Dict[str, Any]]) -> List[TextFeatures]:
        """استخراج خصائص الجمل"""
        try:
            features_list = []
            
            for sentence_data in sentences:
                sentence = sentence_data['text']
                timestamp = sentence_data['start_time']
                
                if not sentence.strip():
                    continue
                
                # تحليل المشاعر
                sentiment_score = self._analyze_sentence_sentiment(sentence)
                emotion_scores = self._analyze_sentence_emotions(sentence)
                
                # استخراج الكلمات المفتاحية
                keywords = self._extract_keywords(sentence)
                viral_keywords = self._extract_viral_keywords(sentence)
                
                # حساب نقاط مختلفة
                readability = self._calculate_sentence_readability(sentence)
                urgency = self._calculate_urgency_score(sentence)
                engagement = self._calculate_engagement_potential(sentence)
                
                # كشف اللغة
                language = self._detect_sentence_language(sentence)
                
                features = TextFeatures(
                    timestamp=timestamp,
                    text=sentence,
                    sentiment_score=sentiment_score,
                    emotion_scores=emotion_scores,
                    keywords=keywords,
                    viral_keywords=viral_keywords,
                    readability_score=readability,
                    urgency_score=urgency,
                    engagement_potential=engagement,
                    language_detected=language,
                    text_length=len(sentence),
                    word_count=len(sentence.split())
                )
                
                features_list.append(features)
            
            return features_list
            
        except Exception as e:
            logger.error(f"خطأ في استخراج خصائص الجمل: {e}")
            return []

    def _analyze_sentence_sentiment(self, sentence: str) -> float:
        """تحليل مشاعر الجملة"""
        try:
            # استخدام عدة طرق ودمج النتائج
            scores = []
            
            # TextBlob
            if TEXTBLOB_AVAILABLE:
                blob = TextBlob(sentence)
                scores.append(blob.sentiment.polarity)
            
            # VADER
            if self.vader_analyzer:
                vader_score = self.vader_analyzer.polarity_scores(sentence)
                scores.append(vader_score['compound'])
            
            # تحليل أساسي للعربية
            arabic_score = self._arabic_sentiment_analysis(sentence)
            scores.append(arabic_score)
            
            # متوسط النقاط
            if scores:
                return statistics.mean(scores)
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"خطأ في تحليل مشاعر الجملة: {e}")
            return 0.0

    def _analyze_sentence_emotions(self, sentence: str) -> Dict[str, float]:
        """تحليل مشاعر متعددة للجملة"""
        try:
            emotions = {
                'joy': 0.0,
                'anger': 0.0,
                'fear': 0.0,
                'sadness': 0.0,
                'surprise': 0.0,
                'disgust': 0.0,
                'trust': 0.0,
                'anticipation': 0.0
            }
            
            sentence_lower = sentence.lower()
            
            # كلمات مفتاحية للمشاعر
            emotion_keywords = {
                'joy': ['سعيد', 'فرح', 'مبسوط', 'happy', 'joy', 'excited', 'glad'],
                'anger': ['غضب', 'زعل', 'angry', 'mad', 'furious', 'rage'],
                'fear': ['خوف', 'قلق', 'fear', 'scared', 'afraid', 'worried'],
                'sadness': ['حزن', 'زعل', 'sad', 'depressed', 'upset', 'down'],
                'surprise': ['مفاجأة', 'دهشة', 'surprise', 'shocked', 'amazed', 'wow'],
                'disgust': ['اشمئزاز', 'قرف', 'disgust', 'gross', 'yuck', 'awful'],
                'trust': ['ثقة', 'أمان', 'trust', 'confident', 'sure', 'believe'],
                'anticipation': ['ترقب', 'انتظار', 'anticipation', 'excited', 'looking forward']
            }
            
            # حساب نقاط كل مشاعر
            for emotion, keywords in emotion_keywords.items():
                score = 0.0
                for keyword in keywords:
                    if keyword in sentence_lower:
                        score += 0.2
                emotions[emotion] = min(score, 1.0)
            
            # تطبيع النقاط
            total_score = sum(emotions.values())
            if total_score > 0:
                emotions = {k: v/total_score for k, v in emotions.items()}
            
            return emotions
            
        except Exception as e:
            logger.error(f"خطأ في تحليل المشاعر المتعددة: {e}")
            return {}

    def _extract_keywords(self, sentence: str) -> List[str]:
        """استخراج الكلمات المفتاحية"""
        try:
            keywords = []
            words = sentence.split()

            # كلمات مهمة (أطول من 3 أحرف)
            important_words = [word for word in words if len(word) > 3]

            # إزالة كلمات الوقف الأساسية
            stop_words = {'هذا', 'هذه', 'ذلك', 'تلك', 'التي', 'الذي', 'في', 'على', 'من', 'إلى',
                         'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with'}

            keywords = [word for word in important_words if word.lower() not in stop_words]

            return keywords[:5]  # أعلى 5 كلمات

        except Exception as e:
            logger.error(f"خطأ في استخراج الكلمات المفتاحية: {e}")
            return []

    def _extract_viral_keywords(self, sentence: str) -> List[str]:
        """استخراج الكلمات الفيروسية"""
        try:
            viral_keywords = []
            sentence_lower = sentence.lower()

            # البحث في جميع فئات الكلمات الفيروسية
            for lang in ['arabic', 'english']:
                for category, keywords in self.viral_keywords[lang].items():
                    for keyword in keywords:
                        if keyword.lower() in sentence_lower:
                            viral_keywords.append(keyword)

            return list(set(viral_keywords))  # إزالة التكرار

        except Exception as e:
            logger.error(f"خطأ في استخراج الكلمات الفيروسية: {e}")
            return []

    def _count_viral_keywords(self, text: str) -> int:
        """عد الكلمات الفيروسية في النص"""
        try:
            count = 0
            text_lower = text.lower()

            for lang in ['arabic', 'english']:
                for category, keywords in self.viral_keywords[lang].items():
                    for keyword in keywords:
                        count += text_lower.count(keyword.lower())

            return count

        except Exception as e:
            logger.error(f"خطأ في عد الكلمات الفيروسية: {e}")
            return 0

    def _calculate_sentence_readability(self, sentence: str) -> float:
        """حساب سهولة قراءة الجملة"""
        try:
            words = sentence.split()
            if not words:
                return 0.0

            # عوامل سهولة القراءة
            avg_word_length = statistics.mean([len(word) for word in words])
            sentence_length = len(words)

            # كلما قلت الكلمات وقصرت = أسهل في القراءة
            readability = 1.0 - min((avg_word_length / 10) + (sentence_length / 50), 1.0)

            return max(readability, 0.0)

        except Exception as e:
            logger.error(f"خطأ في حساب سهولة القراءة: {e}")
            return 0.5

    def _calculate_urgency_score(self, sentence: str) -> float:
        """حساب نقاط الإلحاح"""
        try:
            urgency_score = 0.0
            sentence_lower = sentence.lower()

            # كلمات الإلحاح
            urgency_words = ['عاجل', 'حصري', 'الآن', 'فوراً', 'سريع', 'urgent', 'now', 'breaking', 'exclusive']

            for word in urgency_words:
                if word in sentence_lower:
                    urgency_score += 0.2

            # علامات التعجب تزيد الإلحاح
            exclamations = len(re.findall(r'[!！]', sentence))
            urgency_score += exclamations * 0.1

            # الكلمات الكبيرة تزيد الإلحاح
            caps_words = len(re.findall(r'\b[A-Z]{2,}\b', sentence))
            urgency_score += caps_words * 0.15

            return min(urgency_score, 1.0)

        except Exception as e:
            logger.error(f"خطأ في حساب نقاط الإلحاح: {e}")
            return 0.0

    def _calculate_engagement_potential(self, sentence: str) -> float:
        """حساب إمكانية التفاعل"""
        try:
            engagement = 0.0

            # الأسئلة تزيد التفاعل
            if re.search(r'[؟?]', sentence):
                engagement += 0.3

            # الدعوات للعمل
            action_words = ['شارك', 'اكتب', 'قل', 'اضغط', 'share', 'comment', 'like', 'subscribe']
            for word in action_words:
                if word.lower() in sentence.lower():
                    engagement += 0.2

            # الكلمات العاطفية
            emotional_words = ['حب', 'كره', 'فرح', 'حزن', 'love', 'hate', 'amazing', 'terrible']
            for word in emotional_words:
                if word.lower() in sentence.lower():
                    engagement += 0.15

            # الكلمات الفيروسية
            viral_count = len(self._extract_viral_keywords(sentence))
            engagement += viral_count * 0.1

            return min(engagement, 1.0)

        except Exception as e:
            logger.error(f"خطأ في حساب إمكانية التفاعل: {e}")
            return 0.0

    def _detect_sentence_language(self, sentence: str) -> str:
        """كشف لغة الجملة"""
        try:
            # كشف بسيط بناءً على الأحرف
            arabic_chars = len(re.findall(r'[\u0600-\u06FF]', sentence))
            english_chars = len(re.findall(r'[a-zA-Z]', sentence))

            if arabic_chars > english_chars:
                return 'arabic'
            elif english_chars > arabic_chars:
                return 'english'
            else:
                return 'mixed'

        except Exception as e:
            logger.error(f"خطأ في كشف اللغة: {e}")
            return 'unknown'

    def _basic_sentiment_analysis(self, text: str) -> float:
        """تحليل مشاعر أساسي"""
        try:
            positive_count = 0
            negative_count = 0
            text_lower = text.lower()

            # عد الكلمات الإيجابية والسلبية
            for word in self.arabic_sentiment_words['positive']:
                positive_count += text_lower.count(word)

            for word in self.arabic_sentiment_words['negative']:
                negative_count += text_lower.count(word)

            # حساب النقاط
            total_sentiment_words = positive_count + negative_count
            if total_sentiment_words == 0:
                return 0.0

            return (positive_count - negative_count) / total_sentiment_words

        except Exception as e:
            logger.error(f"خطأ في التحليل الأساسي للمشاعر: {e}")
            return 0.0

    def _arabic_sentiment_analysis(self, text: str) -> float:
        """تحليل مشاعر خاص بالعربية"""
        try:
            return self._basic_sentiment_analysis(text)
        except Exception as e:
            logger.error(f"خطأ في تحليل المشاعر العربية: {e}")
            return 0.0

    def _detect_text_highlights(self, sentence_features: List[TextFeatures]) -> List[TextHighlight]:
        """كشف اللحظات النصية المميزة"""
        try:
            highlights = []

            for features in sentence_features:
                # فحص معايير مختلفة للحظات المميزة

                # عبارة فيروسية
                if features.viral_keywords and features.engagement_potential > 0.6:
                    highlight = self._create_text_highlight(
                        features, 'viral_phrase', features.engagement_potential
                    )
                    highlights.append(highlight)

                # ذروة عاطفية
                elif abs(features.sentiment_score) > 0.7:
                    highlight = self._create_text_highlight(
                        features, 'emotional_peak', abs(features.sentiment_score)
                    )
                    highlights.append(highlight)

                # دعوة للعمل
                elif features.engagement_potential > 0.5 and any('?' in features.text or word in features.text.lower()
                                                               for word in ['شارك', 'اكتب', 'share', 'comment']):
                    highlight = self._create_text_highlight(
                        features, 'call_to_action', features.engagement_potential
                    )
                    highlights.append(highlight)

                # حقيقة مفاجئة
                elif features.urgency_score > 0.5 and any(word in features.viral_keywords
                                                         for word in ['مفاجأة', 'surprise', 'shocking']):
                    highlight = self._create_text_highlight(
                        features, 'surprising_fact', features.urgency_score
                    )
                    highlights.append(highlight)

                # لحظة مضحكة
                elif any(emotion > 0.5 for emotion in features.emotion_scores.values() if 'joy' in str(emotion)):
                    highlight = self._create_text_highlight(
                        features, 'funny_moment', features.emotion_scores.get('joy', 0)
                    )
                    highlights.append(highlight)

            return highlights

        except Exception as e:
            logger.error(f"خطأ في كشف اللحظات النصية: {e}")
            return []

    def _create_text_highlight(self, features: TextFeatures, highlight_type: str,
                             confidence: float) -> TextHighlight:
        """إنشاء لحظة نصية مميزة"""
        try:
            # تحديد حدود اللحظة
            start_time = features.timestamp
            end_time = features.timestamp + 3.0  # 3 ثوانٍ افتراضية

            # حساب الإمكانية الفيروسية
            viral_potential = self._calculate_text_viral_potential(features)

            # حساب التأثير العاطفي
            emotional_impact = max(features.emotion_scores.values()) if features.emotion_scores else 0.0

            return TextHighlight(
                start_time=start_time,
                end_time=end_time,
                text_segment=features.text,
                highlight_type=highlight_type,
                confidence=min(confidence, 1.0),
                features=features,
                viral_potential=viral_potential,
                emotional_impact=emotional_impact
            )

        except Exception as e:
            logger.error(f"خطأ في إنشاء اللحظة النصية: {e}")
            return None

    def _calculate_text_viral_potential(self, features: TextFeatures) -> float:
        """حساب الإمكانية الفيروسية للنص"""
        try:
            viral_score = 0.0

            # الكلمات الفيروسية
            viral_score += len(features.viral_keywords) * 0.2

            # المشاعر القوية
            viral_score += abs(features.sentiment_score) * 0.3

            # إمكانية التفاعل
            viral_score += features.engagement_potential * 0.3

            # الإلحاح
            viral_score += features.urgency_score * 0.2

            return min(viral_score, 1.0)

        except Exception as e:
            logger.error(f"خطأ في حساب الإمكانية الفيروسية: {e}")
            return 0.0

    def _analyze_emotions_advanced(self, text: str, sentence_features: List[TextFeatures]) -> Dict[str, Any]:
        """تحليل متقدم للمشاعر"""
        try:
            emotions = {
                'overall_sentiment': 0.0,
                'emotion_progression': [],
                'dominant_emotion': 'neutral',
                'emotional_peaks': [],
                'sentiment_stability': 0.0
            }

            if not sentence_features:
                return emotions

            # المشاعر العامة
            sentiments = [f.sentiment_score for f in sentence_features]
            emotions['overall_sentiment'] = statistics.mean(sentiments)

            # استقرار المشاعر
            emotions['sentiment_stability'] = 1.0 - (statistics.stdev(sentiments) if len(sentiments) > 1 else 0)

            # تطور المشاعر
            emotions['emotion_progression'] = [
                {'time': f.timestamp, 'sentiment': f.sentiment_score}
                for f in sentence_features
            ]

            # المشاعر المهيمنة
            all_emotions = defaultdict(list)
            for features in sentence_features:
                for emotion, score in features.emotion_scores.items():
                    all_emotions[emotion].append(score)

            avg_emotions = {emotion: statistics.mean(scores) for emotion, scores in all_emotions.items()}
            emotions['dominant_emotion'] = max(avg_emotions, key=avg_emotions.get) if avg_emotions else 'neutral'

            # الذروات العاطفية
            for features in sentence_features:
                if abs(features.sentiment_score) > 0.7:
                    emotions['emotional_peaks'].append({
                        'time': features.timestamp,
                        'text': features.text,
                        'sentiment': features.sentiment_score,
                        'emotions': features.emotion_scores
                    })

            return emotions

        except Exception as e:
            logger.error(f"خطأ في التحليل المتقدم للمشاعر: {e}")
            return {}

    def _detect_special_text_patterns(self, text: str) -> Dict[str, Any]:
        """كشف أنماط نصية خاصة"""
        try:
            patterns = {}

            # تطبيق جميع الأنماط المحددة
            for pattern_name, pattern_regex in self.text_patterns.items():
                matches = re.findall(pattern_regex, text)
                patterns[f'{pattern_name}_count'] = len(matches)
                patterns[f'{pattern_name}_density'] = len(matches) / max(len(text), 1)

            # أنماط خاصة إضافية
            patterns['avg_sentence_length'] = len(text.split('.'))
            patterns['exclamation_ratio'] = patterns['exclamation_count'] / max(len(text.split()), 1)
            patterns['question_ratio'] = patterns['question_count'] / max(len(text.split()), 1)

            return patterns

        except Exception as e:
            logger.error(f"خطأ في كشف الأنماط الخاصة: {e}")
            return {}

    def _analyze_viral_potential(self, text: str, sentence_features: List[TextFeatures]) -> Dict[str, Any]:
        """تحليل الإمكانية الفيروسية"""
        try:
            viral_analysis = {
                'overall_viral_score': 0.0,
                'viral_keywords_found': [],
                'viral_sentences': [],
                'engagement_factors': {},
                'shareability_score': 0.0
            }

            # جمع جميع الكلمات الفيروسية
            all_viral_keywords = []
            viral_sentences = []

            for features in sentence_features:
                all_viral_keywords.extend(features.viral_keywords)
                if features.viral_keywords or features.engagement_potential > 0.6:
                    viral_sentences.append({
                        'text': features.text,
                        'time': features.timestamp,
                        'viral_score': self._calculate_text_viral_potential(features)
                    })

            viral_analysis['viral_keywords_found'] = list(set(all_viral_keywords))
            viral_analysis['viral_sentences'] = viral_sentences

            # النقاط العامة للفيروسية
            if sentence_features:
                viral_scores = [self._calculate_text_viral_potential(f) for f in sentence_features]
                viral_analysis['overall_viral_score'] = statistics.mean(viral_scores)

            # عوامل التفاعل
            viral_analysis['engagement_factors'] = {
                'questions': len(re.findall(r'[؟?]', text)),
                'exclamations': len(re.findall(r'[!！]', text)),
                'viral_keywords': len(all_viral_keywords),
                'emotional_intensity': statistics.mean([abs(f.sentiment_score) for f in sentence_features]) if sentence_features else 0
            }

            # نقاط القابلية للمشاركة
            shareability = 0.0
            shareability += min(len(all_viral_keywords) * 0.1, 0.4)  # كلمات فيروسية
            shareability += min(viral_analysis['engagement_factors']['questions'] * 0.1, 0.2)  # أسئلة
            shareability += min(viral_analysis['engagement_factors']['emotional_intensity'] * 0.4, 0.4)  # شدة عاطفية

            viral_analysis['shareability_score'] = min(shareability, 1.0)

            return viral_analysis

        except Exception as e:
            logger.error(f"خطأ في تحليل الإمكانية الفيروسية: {e}")
            return {}

    def _calculate_readability(self, text: str) -> Dict[str, float]:
        """حساب مقاييس سهولة القراءة"""
        try:
            words = text.split()
            sentences = re.split(r'[.!?؟!]', text)
            sentences = [s.strip() for s in sentences if s.strip()]

            if not words or not sentences:
                return {'readability_score': 0.0, 'complexity_level': 'unknown'}

            # مقاييس أساسية
            avg_words_per_sentence = len(words) / len(sentences)
            avg_chars_per_word = statistics.mean([len(word) for word in words])

            # حساب سهولة القراءة (مبسط)
            readability = 1.0 - min((avg_words_per_sentence / 20) + (avg_chars_per_word / 10), 1.0)

            # تحديد مستوى التعقيد
            if readability > 0.8:
                complexity = 'easy'
            elif readability > 0.6:
                complexity = 'medium'
            else:
                complexity = 'hard'

            return {
                'readability_score': readability,
                'complexity_level': complexity,
                'avg_words_per_sentence': avg_words_per_sentence,
                'avg_chars_per_word': avg_chars_per_word
            }

        except Exception as e:
            logger.error(f"خطأ في حساب سهولة القراءة: {e}")
            return {}

    def _detect_language(self, text: str) -> Dict[str, Any]:
        """كشف اللغة المستخدمة"""
        try:
            arabic_chars = len(re.findall(r'[\u0600-\u06FF]', text))
            english_chars = len(re.findall(r'[a-zA-Z]', text))
            total_chars = len(text)

            arabic_ratio = arabic_chars / max(total_chars, 1)
            english_ratio = english_chars / max(total_chars, 1)

            if arabic_ratio > 0.5:
                primary_language = 'arabic'
            elif english_ratio > 0.5:
                primary_language = 'english'
            else:
                primary_language = 'mixed'

            return {
                'primary_language': primary_language,
                'arabic_ratio': arabic_ratio,
                'english_ratio': english_ratio,
                'is_multilingual': arabic_ratio > 0.2 and english_ratio > 0.2
            }

        except Exception as e:
            logger.error(f"خطأ في كشف اللغة: {e}")
            return {}

    def _get_empty_analysis(self) -> Dict[str, Any]:
        """إرجاع تحليل فارغ"""
        return {
            'text_length': 0,
            'word_count': 0,
            'sentence_count': 0,
            'global_features': {},
            'sentence_features': [],
            'highlights': [],
            'emotion_analysis': {},
            'special_patterns': {},
            'viral_analysis': {},
            'readability_metrics': {},
            'language_detection': {}
        }

# دوال مساعدة للتوافق مع الأداة الحالية
def analyze_text_advanced(text: str, timestamps: List[float] = None) -> Dict[str, Any]:
    """دالة مبسطة لتحليل النص المتقدم"""
    try:
        analyzer = AdvancedTextAnalyzer()
        return analyzer.analyze_text_comprehensive(text, timestamps)
    except Exception as e:
        logger.error(f"خطأ في تحليل النص المتقدم: {e}")
        return {}

def extract_text_highlights(text: str, timestamps: List[float] = None) -> List[Dict[str, Any]]:
    """استخراج اللحظات النصية المميزة"""
    try:
        analysis = analyze_text_advanced(text, timestamps)
        highlights = analysis.get('highlights', [])
        
        # تحويل إلى تنسيق مبسط
        simple_highlights = []
        for highlight in highlights:
            simple_highlights.append({
                'start_time': highlight.start_time,
                'end_time': highlight.end_time,
                'text': highlight.text_segment,
                'type': highlight.highlight_type,
                'confidence': highlight.confidence,
                'viral_potential': highlight.viral_potential
            })
        
        return simple_highlights
        
    except Exception as e:
        logger.error(f"خطأ في استخراج اللحظات النصية: {e}")
        return []

if __name__ == "__main__":
    # اختبار النظام
    print("🧪 اختبار محلل النصوص المتقدم")
    print("=" * 50)
    
    analyzer = AdvancedTextAnalyzer()
    print(f"✅ تم تهيئة المحلل")
    print(f"📊 TextBlob متوفر: {'نعم' if TEXTBLOB_AVAILABLE else 'لا'}")
    print(f"🔬 NLTK متوفر: {'نعم' if NLTK_AVAILABLE else 'لا'}")
    print(f"📝 أنواع اللحظات النصية: {len(analyzer.text_highlight_types)}")
    
    # عرض أنواع اللحظات النصية
    print("\n📖 أنواع اللحظات النصية المدعومة:")
    for key, value in analyzer.text_highlight_types.items():
        print(f"   - {key}: {value}")
    
    # اختبار تحليل نص
    test_text = "هذا فيديو مذهل! لا أصدق ما رأيته للتو. إنه حقاً رائع ومثير للإعجاب."
    print(f"\n🧪 اختبار تحليل النص:")
    print(f"النص: {test_text}")
    
    analysis = analyzer.analyze_text_comprehensive(test_text)
    print(f"عدد الكلمات: {analysis.get('word_count', 0)}")
    print(f"عدد الجمل: {analysis.get('sentence_count', 0)}")
    
    global_features = analysis.get('global_features', {})
    print(f"المشاعر العامة: {global_features.get('textblob_sentiment', 0):.2f}")
    print(f"كثافة الكلمات الفيروسية: {global_features.get('viral_keyword_density', 0):.2f}")
    
    print("\n🏁 انتهى الاختبار")
