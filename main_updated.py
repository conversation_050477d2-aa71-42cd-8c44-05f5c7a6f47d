#!/usr/bin/env python3
"""
AI Youtube Shorts Generator - نسخة محدثة
أداة لإنشاء مقاطع قصيرة من فيديوهات YouTube الطويلة باستخدام الذكاء الاصطناعي
"""

import os
import sys
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

def check_requirements():
    """فحص المتطلبات الأساسية"""
    try:
        import numpy as np
        import cv2
        import openai
        from Components.YoutubeDownloader_fixed import download_youtube_video
        from Components.Edit import extractAudio
        from Components.Transcription_simple import transcribeAudio
        return True
    except ImportError as e:
        print(f"❌ خطأ: مكتبة مطلوبة غير متوفرة: {e}")
        print("💡 تأكد من تثبيت جميع المتطلبات باستخدام: pip install -r requirements_new.txt")
        return False

def check_api_key():
    """فحص مفتاح OpenAI API"""
    api_key = os.getenv('OPENAI_API')
    if not api_key or api_key == 'your_openai_api_key_here':
        print("❌ خطأ: مفتاح OpenAI API غير موجود أو غير صحيح")
        print("💡 تأكد من وضع مفتاح API الصحيح في ملف .env")
        print("   مثال: OPENAI_API=sk-your-actual-api-key-here")
        return False
    return True

def main():
    """الدالة الرئيسية"""
    print("🎬 مرحباً بك في AI Youtube Shorts Generator")
    print("=" * 50)
    
    # فحص المتطلبات
    if not check_requirements():
        return False
    
    # فحص مفتاح API
    if not check_api_key():
        return False
    
    print("✅ جميع المتطلبات متوفرة!")
    print("\n🔗 أدخل رابط فيديو YouTube:")
    
    try:
        # الحصول على رابط الفيديو من المستخدم
        youtube_url = input("الرابط: ").strip()
        
        if not youtube_url:
            print("❌ لم يتم إدخال رابط!")
            return False
        
        if 'youtube.com' not in youtube_url and 'youtu.be' not in youtube_url:
            print("❌ الرابط المدخل ليس رابط YouTube صحيح!")
            return False
        
        print(f"\n📥 بدء تحميل الفيديو من: {youtube_url}")
        
        # استيراد مكون التحميل المحدث
        from Components.YoutubeDownloader_fixed import download_youtube_video
        
        # تحميل الفيديو
        video_path = download_youtube_video(youtube_url)
        
        if not video_path:
            print("❌ فشل في تحميل الفيديو!")
            return False
        
        print(f"✅ تم تحميل الفيديو بنجاح: {video_path}")
        
        # استخراج الصوت
        print("\n🎵 استخراج الصوت من الفيديو...")
        from Components.Edit import extractAudio
        
        audio_path = extractAudio(video_path)
        if audio_path:
            print(f"✅ تم استخراج الصوت: {audio_path}")
        else:
            print("❌ فشل في استخراج الصوت!")
            return False
        
        # نسخ الصوت إلى نص
        print("\n📝 تحويل الصوت إلى نص...")
        from Components.Transcription_simple import transcribeAudio
        
        transcript = transcribeAudio(audio_path)
        if transcript:
            print("✅ تم تحويل الصوت إلى نص بنجاح!")
            print(f"📄 النص: {transcript[:200]}...")
        else:
            print("❌ فشل في تحويل الصوت إلى نص!")
            return False
        
        print("\n🎉 تم إنجاز المعالجة الأساسية بنجاح!")
        print("📁 الملفات المنتجة:")
        print(f"   - الفيديو: {video_path}")
        print(f"   - الصوت: {audio_path}")
        print(f"   - النص: متوفر في الذاكرة")
        
        print("\n💡 ملاحظة: لإنشاء المقاطع القصيرة، ستحتاج إلى:")
        print("   1. تحليل النص باستخدام GPT-4 لاستخراج أفضل المقاطع")
        print("   2. قص الفيديو حسب الأوقات المحددة")
        print("   3. تطبيق تأثيرات الاقتصاص العمودي")
        
        return True
        
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف العملية بواسطة المستخدم")
        return False
    except Exception as e:
        print(f"\n❌ حدث خطأ غير متوقع: {e}")
        return False

if __name__ == "__main__":
    success = main()
    print("\n" + "=" * 50)
    if success:
        print("✅ انتهت العملية بنجاح!")
    else:
        print("❌ انتهت العملية بخطأ!")
    
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
