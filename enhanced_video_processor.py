#!/usr/bin/env python3
"""
معالج الفيديو المحسن
Enhanced Video Processor

يدمج النظام المبسط لكشف اللحظات المثيرة مع الأداة الحالية
"""

import os
import sys
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

# إضافة مسار المكونات
sys.path.append(os.path.join(os.path.dirname(__file__), 'Components'))

# استيراد النظام المبسط
try:
    from simple_shorts_integration import process_video_for_shorts, check_enhanced_system
    ENHANCED_SYSTEM_AVAILABLE = True
except ImportError:
    ENHANCED_SYSTEM_AVAILABLE = False

logger = logging.getLogger(__name__)

def enhanced_process_video_background(youtube_url: str, use_enhanced: bool = True) -> Dict[str, Any]:
    """معالجة الفيديو المحسنة في الخلفية"""
    try:
        logger.info(f"🚀 بدء المعالجة المحسنة للفيديو: {youtube_url}")
        
        # مرحلة 1: تحميل الفيديو
        status = {"status": "downloading", "message": "جاري تحميل الفيديو...", "progress": 10}
        
        from Components.YoutubeDownloader_fixed import download_youtube_video
        video_path = download_youtube_video(youtube_url)
        
        if not video_path:
            return {"status": "error", "message": "فشل في تحميل الفيديو", "progress": 0}
        
        # مرحلة 2: استخراج الصوت
        status = {"status": "extracting", "message": "استخراج الصوت...", "progress": 30}
        
        from Components.Edit import extractAudio
        audio_path = extractAudio(video_path)
        
        if not audio_path:
            return {"status": "error", "message": "فشل في استخراج الصوت", "progress": 0}
        
        # مرحلة 3: تحويل الصوت إلى نص
        status = {"status": "transcribing", "message": "تحويل الصوت إلى نص...", "progress": 50}
        
        from Components.Transcription_simple import transcribeAudio
        transcript = transcribeAudio(audio_path)
        
        # مرحلة 4: التحليل المحسن للحظات المثيرة
        if use_enhanced and ENHANCED_SYSTEM_AVAILABLE:
            status = {"status": "analyzing", "message": "🔍 كشف اللحظات المثيرة باستخدام النظام المحسن...", "progress": 70}
            
            # استخدام النظام المحسن
            enhanced_result = process_video_for_shorts(video_path, target_clips=3)
            
            if enhanced_result.get('success', False):
                highlights = convert_enhanced_clips_to_highlights(enhanced_result['clips'])
                analysis_method = "enhanced_excitement_detection"
                logger.info(f"✅ تم كشف {len(highlights)} لحظة مثيرة بالنظام المحسن")
            else:
                # العودة للنظام التقليدي
                highlights = analyze_video_with_traditional_ai(transcript)
                analysis_method = "traditional_ai_fallback"
                logger.warning("⚠️ فشل النظام المحسن، تم استخدام النظام التقليدي")
        else:
            status = {"status": "analyzing", "message": "تحليل الفيديو باستخدام الذكاء الاصطناعي...", "progress": 70}
            
            # استخدام النظام التقليدي
            highlights = analyze_video_with_traditional_ai(transcript)
            analysis_method = "traditional_ai"
        
        # مرحلة 5: إنشاء معلومات الفيديو المحسنة
        status = {"status": "finalizing", "message": "إنهاء المعالجة...", "progress": 90}
        
        video_info = create_enhanced_video_info(
            youtube_url, video_path, audio_path, transcript, highlights, analysis_method
        )
        
        # مرحلة 6: الانتهاء
        status = {"status": "completed", "message": "✅ تم إنجاز المعالجة المحسنة بنجاح!", "progress": 100}
        
        return {
            "status": "success",
            "video_info": video_info,
            "enhanced_used": use_enhanced and ENHANCED_SYSTEM_AVAILABLE,
            "analysis_method": analysis_method,
            "highlights_count": len(highlights)
        }
        
    except Exception as e:
        logger.error(f"❌ خطأ في المعالجة المحسنة: {e}")
        return {"status": "error", "message": f"خطأ في المعالجة: {str(e)}", "progress": 0}

def convert_enhanced_clips_to_highlights(clips: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """تحويل المقاطع المحسنة إلى تنسيق اللحظات المميزة"""
    try:
        highlights = []
        
        for clip in clips:
            start_time = clip.get('start_time', 0)
            end_time = clip.get('end_time', 30)
            
            # تحويل الثواني إلى تنسيق MM:SS
            start_formatted = f"{int(start_time//60):02d}:{int(start_time%60):02d}"
            end_formatted = f"{int(end_time//60):02d}:{int(end_time%60):02d}"
            
            # إنشاء وصف محسن
            description = clip.get('description', 'مقطع مثير')
            excitement_score = clip.get('excitement_score', 0.5)
            clip_type = clip.get('type', 'highlight')
            
            # تحسين الوصف
            if excitement_score > 0.8:
                description = f"🔥 {description} (إثارة عالية جداً)"
            elif excitement_score > 0.6:
                description = f"⭐ {description} (إثارة عالية)"
            elif excitement_score > 0.4:
                description = f"✨ {description} (إثارة متوسطة)"
            
            highlight = {
                "start": start_formatted,
                "end": end_formatted,
                "start_seconds": start_time,
                "end_seconds": end_time,
                "duration": end_time - start_time,
                "text": clip.get('title_suggestion', f'مقطع مثير {len(highlights) + 1}'),
                "description": description,
                "excitement_score": excitement_score,
                "confidence": clip.get('confidence', excitement_score),
                "type": clip_type,
                "quality": clip.get('quality', 'جيد'),
                "recommended": clip.get('recommended', False),
                "reasons": clip.get('reasons', []),
                "tags": clip.get('tags', []),
                "method": "enhanced_detection"
            }
            
            highlights.append(highlight)
        
        # ترتيب حسب نقاط الإثارة
        highlights.sort(key=lambda x: x.get('excitement_score', 0), reverse=True)
        
        return highlights
        
    except Exception as e:
        logger.error(f"خطأ في تحويل المقاطع: {e}")
        return []

def analyze_video_with_traditional_ai(transcript: str) -> List[Dict[str, Any]]:
    """تحليل الفيديو باستخدام الذكاء الاصطناعي التقليدي"""
    try:
        # محاولة استخدام Gemini أولاً
        try:
            from Components.GeminiTasks import GetHighlightWithGemini
            highlights = GetHighlightWithGemini(transcript)
            if highlights:
                logger.info("✅ تم استخدام Gemini AI لتحليل الفيديو")
                return enhance_traditional_highlights(highlights, "gemini")
        except Exception as e:
            logger.warning(f"⚠️ فشل في استخدام Gemini: {e}")

        # محاولة استخدام OpenAI كبديل
        try:
            from Components.LanguageTasks import GetHighlight
            start_time, end_time = GetHighlight(transcript)
            if start_time and end_time:
                logger.info("✅ تم استخدام OpenAI لتحليل الفيديو")
                highlight = {
                    "start": f"{start_time//60:02d}:{start_time%60:02d}",
                    "end": f"{end_time//60:02d}:{end_time%60:02d}",
                    "start_seconds": start_time,
                    "end_seconds": end_time,
                    "duration": end_time - start_time,
                    "text": "مقطع مميز بواسطة OpenAI",
                    "description": "تم استخراجه باستخدام GPT-4",
                    "excitement_score": 0.7,
                    "confidence": 0.8,
                    "method": "openai"
                }
                return [highlight]
        except Exception as e:
            logger.warning(f"⚠️ فشل في استخدام OpenAI: {e}")

        # استخدام مقاطع افتراضية محسنة
        return create_fallback_highlights()
        
    except Exception as e:
        logger.error(f"خطأ في التحليل التقليدي: {e}")
        return create_fallback_highlights()

def enhance_traditional_highlights(highlights: List[Dict[str, Any]], source: str) -> List[Dict[str, Any]]:
    """تحسين اللحظات المميزة التقليدية"""
    try:
        enhanced_highlights = []
        
        for i, highlight in enumerate(highlights):
            # إضافة معلومات محسنة
            enhanced_highlight = highlight.copy()
            
            # إضافة نقاط الإثارة المقدرة
            if source == "gemini":
                enhanced_highlight['excitement_score'] = 0.8  # Gemini عادة دقيق
                enhanced_highlight['confidence'] = 0.9
            else:
                enhanced_highlight['excitement_score'] = 0.6
                enhanced_highlight['confidence'] = 0.7
            
            # إضافة معلومات إضافية
            enhanced_highlight['method'] = f"traditional_{source}"
            enhanced_highlight['quality'] = 'جيد جداً' if source == "gemini" else 'جيد'
            enhanced_highlight['recommended'] = i < 2  # أول مقطعين موصى بهما
            
            # حساب المدة بالثواني
            start_time = parse_time_to_seconds(highlight.get('start', '00:00'))
            end_time = parse_time_to_seconds(highlight.get('end', '00:30'))
            
            enhanced_highlight['start_seconds'] = start_time
            enhanced_highlight['end_seconds'] = end_time
            enhanced_highlight['duration'] = end_time - start_time
            
            enhanced_highlights.append(enhanced_highlight)
        
        return enhanced_highlights
        
    except Exception as e:
        logger.error(f"خطأ في تحسين اللحظات التقليدية: {e}")
        return highlights

def parse_time_to_seconds(time_str: str) -> float:
    """تحويل تنسيق الوقت MM:SS إلى ثوانٍ"""
    try:
        if ':' in time_str:
            parts = time_str.split(':')
            minutes = int(parts[0])
            seconds = int(parts[1])
            return minutes * 60 + seconds
        else:
            return float(time_str)
    except:
        return 0.0

def create_fallback_highlights() -> List[Dict[str, Any]]:
    """إنشاء لحظات مميزة احتياطية"""
    try:
        fallback_highlights = [
            {
                "start": "00:10",
                "end": "00:35",
                "start_seconds": 10,
                "end_seconds": 35,
                "duration": 25,
                "text": "مقطع البداية المثير",
                "description": "✨ بداية الفيديو - عادة ما تكون مثيرة",
                "excitement_score": 0.6,
                "confidence": 0.5,
                "method": "fallback",
                "quality": "مقبول",
                "recommended": True
            },
            {
                "start": "01:00",
                "end": "01:25",
                "start_seconds": 60,
                "end_seconds": 85,
                "duration": 25,
                "text": "مقطع الوسط المميز",
                "description": "⭐ وسط الفيديو - قد يحتوي على محتوى مثير",
                "excitement_score": 0.5,
                "confidence": 0.4,
                "method": "fallback",
                "quality": "مقبول",
                "recommended": False
            }
        ]
        
        logger.info("📝 تم إنشاء لحظات مميزة احتياطية")
        return fallback_highlights
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء اللحظات الاحتياطية: {e}")
        return []

def create_enhanced_video_info(youtube_url: str, video_path: str, audio_path: str, 
                             transcript: str, highlights: List[Dict[str, Any]], 
                             analysis_method: str) -> Dict[str, Any]:
    """إنشاء معلومات فيديو محسنة"""
    try:
        # معلومات أساسية
        video_info = {
            "url": youtube_url,
            "title": os.path.basename(video_path).replace('.mp4', ''),
            "video_path": video_path,
            "audio_path": audio_path,
            "transcript": transcript,
            "processed_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "highlights": highlights,
            "shorts": [],
            "analysis_method": analysis_method
        }
        
        # إضافة معلومات محسنة
        if highlights:
            # إحصائيات اللحظات المميزة
            excitement_scores = [h.get('excitement_score', 0) for h in highlights]
            video_info['highlights_stats'] = {
                'count': len(highlights),
                'avg_excitement': sum(excitement_scores) / len(excitement_scores),
                'max_excitement': max(excitement_scores),
                'recommended_count': sum(1 for h in highlights if h.get('recommended', False)),
                'total_duration': sum(h.get('duration', 0) for h in highlights)
            }
            
            # أفضل مقطع
            best_highlight = max(highlights, key=lambda x: x.get('excitement_score', 0))
            video_info['best_highlight'] = {
                'start': best_highlight.get('start', '00:00'),
                'end': best_highlight.get('end', '00:30'),
                'excitement_score': best_highlight.get('excitement_score', 0),
                'description': best_highlight.get('description', 'أفضل مقطع')
            }
            
            # توصيات
            video_info['recommendations'] = generate_video_recommendations(highlights, analysis_method)
        
        # معلومات تقنية
        try:
            import cv2
            cap = cv2.VideoCapture(video_path)
            if cap.isOpened():
                fps = cap.get(cv2.CAP_PROP_FPS)
                total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                duration = total_frames / fps if fps > 0 else 0
                
                video_info['duration'] = f"{int(duration//60):02d}:{int(duration%60):02d}"
                video_info['duration_seconds'] = duration
                video_info['fps'] = fps
                
                cap.release()
        except:
            video_info['duration'] = "غير محدد"
        
        return video_info
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء معلومات الفيديو: {e}")
        return {}

def generate_video_recommendations(highlights: List[Dict[str, Any]], method: str) -> List[str]:
    """إنشاء توصيات للفيديو"""
    try:
        recommendations = []
        
        if not highlights:
            recommendations.append("❌ لم يتم العثور على لحظات مميزة")
            return recommendations
        
        # توصيات حسب الجودة
        avg_excitement = sum(h.get('excitement_score', 0) for h in highlights) / len(highlights)
        
        if avg_excitement > 0.7:
            recommendations.append("🌟 فيديو ممتاز! اللحظات المميزة عالية الجودة")
        elif avg_excitement > 0.5:
            recommendations.append("✅ فيديو جيد - يحتوي على لحظات مثيرة")
        else:
            recommendations.append("⚠️ فيديو مقبول - فكر في البحث عن محتوى أكثر إثارة")
        
        # توصيات حسب العدد
        recommended_count = sum(1 for h in highlights if h.get('recommended', False))
        if recommended_count > 0:
            recommendations.append(f"🎯 {recommended_count} مقطع موصى به للنشر")
        
        # توصيات حسب الطريقة
        if method == "enhanced_excitement_detection":
            recommendations.append("🚀 تم استخدام النظام المحسن - نتائج دقيقة")
        elif method == "traditional_ai":
            recommendations.append("🤖 تم استخدام الذكاء الاصطناعي التقليدي")
        else:
            recommendations.append("🔄 تم استخدام نظام احتياطي")
        
        # توصية عامة
        recommendations.append("💡 أضف موسيقى وتأثيرات لزيادة جاذبية المقاطع")
        
        return recommendations
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء التوصيات: {e}")
        return ["تم معالجة الفيديو بنجاح"]

def get_enhanced_system_status() -> Dict[str, Any]:
    """الحصول على حالة النظام المحسن"""
    try:
        if ENHANCED_SYSTEM_AVAILABLE:
            enhanced_status = check_enhanced_system()
            return {
                'available': True,
                'status': enhanced_status.get('status', 'ready'),
                'message': enhanced_status.get('message', 'النظام المحسن جاهز'),
                'capabilities': [
                    'كشف اللحظات المثيرة المتقدم',
                    'تحليل الحركة والإثارة',
                    'تقييم جودة المقاطع',
                    'توصيات ذكية'
                ]
            }
        else:
            return {
                'available': False,
                'status': 'unavailable',
                'message': 'النظام المحسن غير متوفر - سيتم استخدام النظام التقليدي',
                'capabilities': []
            }
    except Exception as e:
        logger.error(f"خطأ في فحص حالة النظام: {e}")
        return {'available': False, 'status': 'error', 'message': str(e)}

if __name__ == "__main__":
    # اختبار النظام المحسن
    print("🧪 اختبار معالج الفيديو المحسن")
    print("=" * 50)
    
    status = get_enhanced_system_status()
    print(f"📊 حالة النظام المحسن: {status['message']}")
    print(f"🔧 متوفر: {'نعم' if status['available'] else 'لا'}")
    
    if status['available']:
        print("✨ القدرات المتاحة:")
        for capability in status['capabilities']:
            print(f"   - {capability}")
    
    print("\n🎯 النظام جاهز للاستخدام!")
    print("💡 استخدم: enhanced_process_video_background(youtube_url)")
    
    print("\n🏁 انتهى الاختبار")
