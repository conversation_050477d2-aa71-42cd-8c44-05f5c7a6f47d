#!/usr/bin/env python3
"""
AI Youtube Shorts Generator - واجهة ويب تفاعلية
تطبيق ويب شامل لإنشاء مقاطع قصيرة من فيديوهات YouTube
"""

import os
import sys
import json
import threading
import webbrowser
from datetime import datetime
from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for
from werkzeug.utils import secure_filename
import time
from database import video_db, shorts_db
from api_checker import APIChecker

# إعداد Flask
app = Flask(__name__)
app.secret_key = 'ai-youtube-shorts-generator-2025'

# متغيرات عامة
current_status = {"status": "ready", "message": "جاهز للبدء", "progress": 0}
current_video_info = {}

def check_requirements():
    """فحص المتطلبات الأساسية والـ APIs"""
    try:
        # فحص المكتبات الأساسية
        import numpy as np
        import cv2
        import openai
        from dotenv import load_dotenv
        load_dotenv()

        # فحص APIs باستخدام APIChecker
        api_checker = APIChecker()
        api_results = api_checker.check_all_apis()
        api_status = api_checker.get_api_status_summary()

        # التحقق من APIs المطلوبة
        if not api_status['all_required_ok']:
            missing_apis = []
            if not api_status['internet_ok']:
                missing_apis.append("الإنترنت")
            if not api_status['youtube_ok']:
                missing_apis.append("YouTube")

            return False, f"مشكلة في الاتصال: {', '.join(missing_apis)}"

        # التحقق من AI APIs (اختياري)
        if api_status['ai_available']:
            ai_provider = "Gemini" if api_status['gemini_ok'] else "OpenAI"
            return True, f"جميع المتطلبات متوفرة (مع {ai_provider} API)"
        else:
            return True, "المتطلبات الأساسية متوفرة (بدون AI APIs - ميزات محدودة)"

    except ImportError as e:
        return False, f"مكتبة مطلوبة غير متوفرة: {e}"
    except Exception as e:
        return False, f"خطأ في فحص المتطلبات: {str(e)}"

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html')

@app.route('/api/status')
def get_status():
    """الحصول على حالة النظام"""
    requirements_ok, message = check_requirements()
    return jsonify({
        "requirements": requirements_ok,
        "message": message,
        "current_status": current_status,
        "processed_videos": len(video_db.get_all_videos())
    })

@app.route('/api/process_video', methods=['POST'])
def process_video():
    """معالجة فيديو YouTube"""
    global current_status, current_video_info
    
    try:
        data = request.get_json()
        youtube_url = data.get('url', '').strip()
        
        if not youtube_url:
            return jsonify({"success": False, "error": "لم يتم إدخال رابط!"})
        
        if 'youtube.com' not in youtube_url and 'youtu.be' not in youtube_url:
            return jsonify({"success": False, "error": "الرابط المدخل ليس رابط YouTube صحيح!"})
        
        # بدء المعالجة في خيط منفصل
        thread = threading.Thread(target=process_video_background, args=(youtube_url,))
        thread.daemon = True
        thread.start()
        
        return jsonify({"success": True, "message": "بدأت معالجة الفيديو..."})
        
    except Exception as e:
        return jsonify({"success": False, "error": f"خطأ: {str(e)}"})

def process_video_background(youtube_url):
    """معالجة الفيديو في الخلفية"""
    global current_status, current_video_info
    
    try:
        # تحديث الحالة
        current_status = {"status": "downloading", "message": "جاري تحميل الفيديو...", "progress": 10}
        
        # تحميل الفيديو
        from Components.YoutubeDownloader_fixed import download_youtube_video
        video_path = download_youtube_video(youtube_url)
        
        if not video_path:
            current_status = {"status": "error", "message": "فشل في تحميل الفيديو", "progress": 0}
            return
        
        current_status = {"status": "extracting", "message": "استخراج الصوت...", "progress": 30}
        
        # استخراج الصوت
        from Components.Edit import extractAudio
        audio_path = extractAudio(video_path)
        
        if not audio_path:
            current_status = {"status": "error", "message": "فشل في استخراج الصوت", "progress": 0}
            return
        
        current_status = {"status": "transcribing", "message": "تحويل الصوت إلى نص...", "progress": 60}
        
        # تحويل الصوت إلى نص
        from Components.Transcription_simple import transcribeAudio
        transcript = transcribeAudio(audio_path)
        
        if not transcript:
            current_status = {"status": "error", "message": "فشل في تحويل الصوت إلى نص", "progress": 0}
            return
        
        current_status = {"status": "analyzing", "message": "تحليل الفيديو باستخدام الذكاء الاصطناعي...", "progress": 80}

        # تحليل الفيديو باستخدام Gemini AI
        highlights = analyze_video_with_ai(transcript)
        
        # حفظ معلومات الفيديو
        video_info = {
            "url": youtube_url,
            "title": os.path.basename(video_path).replace('.mp4', ''),
            "video_path": video_path,
            "audio_path": audio_path,
            "transcript": transcript,
            "processed_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "duration": "غير محدد",
            "highlights": highlights,
            "shorts": []  # قائمة المقاطع القصيرة المنشأة
        }

        # حفظ في قاعدة البيانات
        if video_db.add_video(video_info):
            # الحصول على الفيديو المحفوظ مع المعرف
            saved_videos = video_db.get_all_videos()
            current_video_info = saved_videos[-1] if saved_videos else video_info
        else:
            current_video_info = video_info
        
        current_status = {"status": "completed", "message": "تم إنجاز المعالجة بنجاح!", "progress": 100}
        
    except Exception as e:
        current_status = {"status": "error", "message": f"خطأ في المعالجة: {str(e)}", "progress": 0}

def analyze_video_with_ai(transcript: str) -> list:
    """تحليل الفيديو باستخدام الذكاء الاصطناعي (Gemini أو OpenAI)"""
    try:
        # محاولة استخدام Gemini أولاً
        try:
            from Components.GeminiTasks import GetHighlightWithGemini
            highlights = GetHighlightWithGemini(transcript)
            if highlights:
                print("✅ تم استخدام Gemini AI لتحليل الفيديو")
                return highlights
        except Exception as e:
            print(f"⚠️ فشل في استخدام Gemini: {e}")

        # محاولة استخدام OpenAI كبديل
        try:
            from Components.LanguageTasks import GetHighlight
            start_time, end_time = GetHighlight(transcript)
            if start_time and end_time:
                print("✅ تم استخدام OpenAI لتحليل الفيديو")
                return [{
                    "start": f"{start_time//60:02d}:{start_time%60:02d}",
                    "end": f"{end_time//60:02d}:{end_time%60:02d}",
                    "text": "مقطع مميز بواسطة OpenAI",
                    "description": "تم استخراجه باستخدام GPT-4"
                }]
        except Exception as e:
            print(f"⚠️ فشل في استخدام OpenAI: {e}")

        # استخدام مقاطع افتراضية
        print("⚠️ استخدام المقاطع الافتراضية")
        return [
            {"start": "00:10", "end": "00:40", "text": "مقطع مثير للاهتمام 1", "description": "بداية قوية"},
            {"start": "01:30", "end": "02:00", "text": "مقطع مثير للاهتمام 2", "description": "نقطة مهمة"},
            {"start": "02:45", "end": "03:15", "text": "مقطع مثير للاهتمام 3", "description": "خاتمة قوية"}
        ]

    except Exception as e:
        print(f"❌ خطأ في تحليل الفيديو: {e}")
        return [
            {"start": "00:10", "end": "00:40", "text": "مقطع افتراضي", "description": "تم إنشاؤه تلقائياً"}
        ]

def create_actual_short(video_info: dict, start_time: str, end_time: str, title: str) -> str:
    """إنشاء ملف المقطع القصير الفعلي"""
    try:
        video_path = video_info.get('video_path')
        if not video_path or not os.path.exists(video_path):
            print(f"❌ ملف الفيديو غير موجود: {video_path}")
            return None

        # تحويل الأوقات إلى ثواني
        start_seconds = time_to_seconds(start_time)
        end_seconds = time_to_seconds(end_time)

        # إنشاء مجلد للمقاطع القصيرة
        shorts_dir = "shorts"
        if not os.path.exists(shorts_dir):
            os.makedirs(shorts_dir)

        # تنظيف اسم الملف
        safe_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_title = safe_title.replace(' ', '_')

        # مسار الملف الجديد
        output_path = os.path.join(shorts_dir, f"{safe_title}_{start_seconds}_{end_seconds}.mp4")

        # استخدام مكون التحرير لقص الفيديو
        from Components.Edit import crop_video
        crop_video(video_path, output_path, start_seconds, end_seconds)

        if os.path.exists(output_path):
            print(f"✅ تم إنشاء المقطع القصير: {output_path}")
            return output_path
        else:
            print("❌ فشل في إنشاء المقطع القصير")
            return None

    except Exception as e:
        print(f"❌ خطأ في إنشاء المقطع القصير: {e}")
        return None

def time_to_seconds(time_str: str) -> int:
    """تحويل وقت MM:SS إلى ثواني"""
    try:
        if ':' in time_str:
            parts = time_str.split(':')
            if len(parts) == 2:
                minutes, seconds = map(int, parts)
                return minutes * 60 + seconds
        return int(float(time_str))
    except:
        return 0

@app.route('/api/videos')
def get_videos():
    """الحصول على قائمة الفيديوهات المعالجة"""
    return jsonify({"videos": video_db.get_all_videos()})

@app.route('/api/video/<int:video_id>')
def get_video_details(video_id):
    """الحصول على تفاصيل فيديو محدد"""
    video = video_db.get_video_by_id(video_id)
    if video:
        return jsonify({"success": True, "video": video})
    return jsonify({"success": False, "error": "الفيديو غير موجود"})

@app.route('/api/create_short', methods=['POST'])
def create_short():
    """إنشاء مقطع قصير"""
    try:
        data = request.get_json()
        video_id = data.get('video_id')
        start_time = data.get('start_time')
        end_time = data.get('end_time')
        title = data.get('title', f"مقطع من {start_time} إلى {end_time}")

        # التحقق من وجود الفيديو
        video = video_db.get_video_by_id(video_id)
        if not video:
            return jsonify({"success": False, "error": "الفيديو غير موجود"})

        # إنشاء الملف الفعلي للمقطع القصير
        short_file_path = create_actual_short(video, start_time, end_time, title)

        # إنشاء معلومات المقطع القصير
        short_info = {
            "video_id": video_id,
            "title": title,
            "start_time": start_time,
            "end_time": end_time,
            "video_title": video.get('title', ''),
            "video_url": video.get('url', ''),
            "status": "تم الإنشاء بنجاح" if short_file_path else "فشل في إنشاء الملف",
            "file_path": short_file_path
        }

        # حفظ في قاعدة بيانات المقاطع القصيرة
        if shorts_db.add_short(short_info):
            return jsonify({"success": True, "short": short_info})
        else:
            return jsonify({"success": False, "error": "فشل في حفظ المقطع القصير"})

    except Exception as e:
        return jsonify({"success": False, "error": f"خطأ في إنشاء المقطع: {str(e)}"})

@app.route('/api/settings', methods=['GET', 'POST'])
def settings():
    """إعدادات التطبيق"""
    if request.method == 'POST':
        data = request.get_json()
        # حفظ الإعدادات (محاكاة)
        return jsonify({"success": True, "message": "تم حفظ الإعدادات"})
    
    # إرجاع الإعدادات الحالية
    settings_data = {
        "api_key_set": bool(os.getenv('OPENAI_API')),
        "output_quality": "high",
        "auto_crop": True,
        "language": "ar"
    }
    return jsonify(settings_data)

@app.route('/api/shorts')
def get_shorts():
    """الحصول على جميع المقاطع القصيرة"""
    return jsonify({"shorts": shorts_db.get_all_shorts()})

@app.route('/api/shorts/<int:video_id>')
def get_shorts_by_video(video_id):
    """الحصول على المقاطع القصيرة لفيديو معين"""
    shorts = shorts_db.get_shorts_by_video_id(video_id)
    return jsonify({"shorts": shorts})

@app.route('/api/search_videos')
def search_videos():
    """البحث في الفيديوهات"""
    query = request.args.get('q', '')
    if not query:
        return jsonify({"videos": []})

    results = video_db.search_videos(query)
    return jsonify({"videos": results})

@app.route('/api/statistics')
def get_statistics():
    """الحصول على إحصائيات النظام"""
    video_stats = video_db.get_statistics()
    shorts_count = len(shorts_db.get_all_shorts())

    stats = {
        **video_stats,
        "total_shorts": shorts_count
    }
    return jsonify(stats)

@app.route('/api/delete_video/<int:video_id>', methods=['DELETE'])
def delete_video(video_id):
    """حذف فيديو ومقاطعه القصيرة"""
    try:
        # حذف الفيديو من قاعدة البيانات
        if video_db.delete_video(video_id):
            # حذف المقاطع القصيرة المرتبطة (إذا كانت موجودة)
            shorts = shorts_db.get_shorts_by_video_id(video_id)
            for short in shorts:
                shorts_db.delete_short(short.get('id'))

            return jsonify({"success": True, "message": "تم حذف الفيديو بنجاح"})
        else:
            return jsonify({"success": False, "error": "فشل في حذف الفيديو"})
    except Exception as e:
        return jsonify({"success": False, "error": f"خطأ في حذف الفيديو: {str(e)}"})

@app.route('/api/export_data')
def export_data():
    """تصدير جميع البيانات"""
    try:
        export_data = {
            "videos": video_db.get_all_videos(),
            "shorts": shorts_db.get_all_shorts(),
            "exported_at": datetime.now().isoformat()
        }
        return jsonify(export_data)
    except Exception as e:
        return jsonify({"success": False, "error": f"خطأ في تصدير البيانات: {str(e)}"})

@app.route('/api/check_apis')
def check_apis():
    """فحص حالة جميع APIs"""
    try:
        api_checker = APIChecker()
        results = api_checker.check_all_apis()
        status = api_checker.get_api_status_summary()

        return jsonify({
            "success": True,
            "results": results,
            "status": status,
            "overall_status": "ready" if status['all_required_ok'] else "error"
        })
    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"خطأ في فحص APIs: {str(e)}",
            "overall_status": "error"
        })

@app.route('/favicon.ico')
def favicon():
    """إرجاع favicon بسيط"""
    return '', 204  # No Content

@app.route('/api/download_video/<int:video_id>')
def download_video(video_id):
    """تحميل ملف الفيديو الأصلي"""
    try:
        video = video_db.get_video_by_id(video_id)
        if not video:
            return jsonify({"success": False, "error": "الفيديو غير موجود"})

        video_path = video.get('video_path')
        if not video_path or not os.path.exists(video_path):
            return jsonify({"success": False, "error": "ملف الفيديو غير موجود"})

        return send_file(
            video_path,
            as_attachment=True,
            download_name=f"{video.get('title', 'video')}.mp4"
        )
    except Exception as e:
        return jsonify({"success": False, "error": f"خطأ في تحميل الفيديو: {str(e)}"})

@app.route('/api/download_audio/<int:video_id>')
def download_audio(video_id):
    """تحميل ملف الصوت المستخرج"""
    try:
        video = video_db.get_video_by_id(video_id)
        if not video:
            return jsonify({"success": False, "error": "الفيديو غير موجود"})

        audio_path = video.get('audio_path')
        if not audio_path or not os.path.exists(audio_path):
            return jsonify({"success": False, "error": "ملف الصوت غير موجود"})

        return send_file(
            audio_path,
            as_attachment=True,
            download_name=f"{video.get('title', 'audio')}.wav"
        )
    except Exception as e:
        return jsonify({"success": False, "error": f"خطأ في تحميل الصوت: {str(e)}"})

@app.route('/api/download_transcript/<int:video_id>')
def download_transcript(video_id):
    """تحميل النص المنسوخ كملف نصي"""
    try:
        video = video_db.get_video_by_id(video_id)
        if not video:
            return jsonify({"success": False, "error": "الفيديو غير موجود"})

        transcript = video.get('transcript', '')
        if not transcript:
            return jsonify({"success": False, "error": "النص المنسوخ غير متوفر"})

        # إنشاء ملف نصي مؤقت
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(f"النص المنسوخ للفيديو: {video.get('title', 'غير محدد')}\n")
            f.write(f"الرابط: {video.get('url', 'غير محدد')}\n")
            f.write(f"تاريخ المعالجة: {video.get('processed_at', 'غير محدد')}\n")
            f.write("="*50 + "\n\n")
            f.write(transcript)
            temp_path = f.name

        return send_file(
            temp_path,
            as_attachment=True,
            download_name=f"{video.get('title', 'transcript')}.txt"
        )
    except Exception as e:
        return jsonify({"success": False, "error": f"خطأ في تحميل النص: {str(e)}"})

@app.route('/api/download_short/<int:short_id>')
def download_short(short_id):
    """تحميل ملف المقطع القصير"""
    try:
        # البحث عن المقطع القصير
        shorts = shorts_db.get_all_shorts()
        short = next((s for s in shorts if s.get('id') == short_id), None)

        if not short:
            return jsonify({"success": False, "error": "المقطع القصير غير موجود"})

        file_path = short.get('file_path')
        if not file_path or not os.path.exists(file_path):
            return jsonify({"success": False, "error": "ملف المقطع القصير غير موجود"})

        return send_file(
            file_path,
            as_attachment=True,
            download_name=f"{short.get('title', 'short')}.mp4"
        )
    except Exception as e:
        return jsonify({"success": False, "error": f"خطأ في تحميل المقطع القصير: {str(e)}"})

@app.route('/api/generate_short_file/<int:short_id>', methods=['POST'])
def generate_short_file(short_id):
    """إنشاء ملف MP4 للمقطع القصير"""
    try:
        # البحث عن المقطع القصير
        shorts = shorts_db.get_all_shorts()
        short = next((s for s in shorts if s.get('id') == short_id), None)

        if not short:
            return jsonify({"success": False, "error": "المقطع القصير غير موجود"})

        # البحث عن الفيديو الأصلي
        video = video_db.get_video_by_id(short.get('video_id'))
        if not video:
            return jsonify({"success": False, "error": "الفيديو الأصلي غير موجود"})

        # إنشاء الملف الفعلي
        file_path = create_actual_short(
            video,
            short.get('start_time'),
            short.get('end_time'),
            short.get('title')
        )

        if file_path:
            # تحديث قاعدة البيانات
            updated_shorts = []
            for s in shorts:
                if s.get('id') == short_id:
                    s['file_path'] = file_path
                    s['status'] = "تم الإنشاء بنجاح"
                updated_shorts.append(s)

            # حفظ التحديث
            shorts_db.shorts = updated_shorts
            shorts_db._save_database()

            return jsonify({"success": True, "message": "تم إنشاء الملف بنجاح", "file_path": file_path})
        else:
            return jsonify({"success": False, "error": "فشل في إنشاء الملف"})

    except Exception as e:
        return jsonify({"success": False, "error": f"خطأ في إنشاء الملف: {str(e)}"})

@app.route('/api/delete_short/<int:short_id>', methods=['DELETE'])
def delete_short(short_id):
    """حذف مقطع قصير"""
    try:
        # البحث عن المقطع وحذف الملف إذا كان موجوداً
        shorts = shorts_db.get_all_shorts()
        short = next((s for s in shorts if s.get('id') == short_id), None)

        if short and short.get('file_path') and os.path.exists(short.get('file_path')):
            os.remove(short.get('file_path'))

        # حذف من قاعدة البيانات
        if shorts_db.delete_short(short_id):
            return jsonify({"success": True, "message": "تم حذف المقطع بنجاح"})
        else:
            return jsonify({"success": False, "error": "فشل في حذف المقطع"})

    except Exception as e:
        return jsonify({"success": False, "error": f"خطأ في حذف المقطع: {str(e)}"})

def open_browser():
    """فتح المتصفح تلقائياً"""
    time.sleep(1.5)  # انتظار حتى يبدأ الخادم
    webbrowser.open('http://localhost:5000')

if __name__ == '__main__':
    print("🚀 بدء تشغيل AI Youtube Shorts Generator - واجهة الويب")
    print("=" * 60)
    print("🌐 سيتم فتح المتصفح تلقائياً...")
    print("📍 الرابط: http://localhost:5000")
    print("⏹️  للإيقاف: اضغط Ctrl+C")
    print("=" * 60)
    
    # فتح المتصفح في خيط منفصل
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # تشغيل الخادم
    try:
        app.run(debug=False, host='0.0.0.0', port=5000, threaded=True)
    except KeyboardInterrupt:
        print("\n⏹️  تم إيقاف الخادم")
        sys.exit(0)
