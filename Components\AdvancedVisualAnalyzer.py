#!/usr/bin/env python3
"""
محلل البصريات المتقدم
Advanced Visual Analysis System

يستخدم MediaPipe وتقنيات متقدمة لتحليل الفيديو وكشف اللحظات البصرية المثيرة
مدمج من الأداة المتقدمة مع تحسينات للأداة الحالية
"""

import cv2
import numpy as np
import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
import json
import time
import statistics
from collections import deque

# محاولة استيراد MediaPipe
try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
except ImportError:
    MEDIAPIPE_AVAILABLE = False
    mp = None

logger = logging.getLogger(__name__)

@dataclass
class VisualFeatures:
    """خصائص بصرية مستخرجة"""
    timestamp: float
    motion_intensity: float
    brightness: float
    contrast: float
    color_variance: float
    edge_density: float
    face_count: int
    face_emotions: Dict[str, float]
    pose_detected: bool
    hand_gestures: List[str]
    scene_complexity: float
    dominant_colors: List[Tuple[int, int, int]]

@dataclass
class VisualHighlight:
    """لحظة بصرية مميزة"""
    start_time: float
    end_time: float
    peak_time: float
    highlight_type: str
    confidence: float
    features: VisualFeatures
    intensity_score: float
    visual_impact: float

class AdvancedVisualAnalyzer:
    """محلل البصريات المتقدم"""
    
    def __init__(self):
        # إعداد MediaPipe
        if MEDIAPIPE_AVAILABLE:
            self.mp_face_detection = mp.solutions.face_detection
            self.mp_face_mesh = mp.solutions.face_mesh
            self.mp_pose = mp.solutions.pose
            self.mp_hands = mp.solutions.hands
            self.mp_drawing = mp.solutions.drawing_utils

            try:
                # إعداد كاشفات متعددة
                self.face_detection = self.mp_face_detection.FaceDetection(
                    model_selection=1, min_detection_confidence=0.5
                )
                self.face_mesh = self.mp_face_mesh.FaceMesh(
                    static_image_mode=False,
                    max_num_faces=10,  # كشف حتى 10 وجوه
                    refine_landmarks=True,
                    min_detection_confidence=0.4,
                    min_tracking_confidence=0.4
                )
                self.pose = self.mp_pose.Pose(
                    static_image_mode=False,
                    model_complexity=1,
                    smooth_landmarks=True,
                    min_detection_confidence=0.5,
                    min_tracking_confidence=0.5
                )
                self.hands = self.mp_hands.Hands(
                    static_image_mode=False,
                    max_num_hands=4,  # كشف حتى 4 أيدي
                    min_detection_confidence=0.5,
                    min_tracking_confidence=0.5
                )
                logger.info("تم تهيئة MediaPipe بنجاح")
            except Exception as e:
                logger.warning(f"فشل في تهيئة MediaPipe: {e}")
                self.face_detection = None
                self.face_mesh = None
                self.pose = None
                self.hands = None
        else:
            logger.warning("MediaPipe غير متوفر - سيتم استخدام تحليل أساسي")
            self.mp_face_detection = None
            self.mp_face_mesh = None
            self.mp_pose = None
            self.mp_hands = None
            self.face_detection = None
            self.face_mesh = None
            self.pose = None
            self.hands = None

            # إعداد كاشف وجه أساسي
            try:
                self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
                logger.info("تم تهيئة كاشف الوجه الأساسي")
            except Exception as e:
                logger.warning(f"فشل في تهيئة كاشف الوجه الأساسي: {e}")
                self.face_cascade = None
        
        # إعدادات التحليل
        self.window_size = 2.0    # نافذة ثانيتين للتحليل
        self.overlap = 1.0        # تداخل ثانية واحدة
        self.frame_skip = 3       # تحليل كل 3 إطارات لتوفير الوقت
        
        # عتبات الكشف
        self.motion_threshold = 0.3
        self.brightness_change_threshold = 0.2
        self.face_count_threshold = 2  # للمشاهد الجماعية
        self.complexity_threshold = 0.6
        
        # أنواع اللحظات البصرية
        self.visual_highlight_types = {
            'motion_peak': 'ذروة حركة',
            'face_reaction': 'ردة فعل وجه',
            'crowd_scene': 'مشهد جماعي',
            'gesture_moment': 'لحظة إيماءة',
            'lighting_change': 'تغيير إضاءة',
            'color_burst': 'انفجار ألوان',
            'close_up': 'لقطة قريبة',
            'wide_shot': 'لقطة واسعة',
            'action_sequence': 'تسلسل حركي',
            'emotional_expression': 'تعبير عاطفي'
        }
        
        # تخزين مؤقت للمقارنة
        self.previous_frames = deque(maxlen=10)
        self.motion_history = deque(maxlen=30)
        self.brightness_history = deque(maxlen=30)
        
        logger.info("تم تهيئة محلل البصريات المتقدم")

    def analyze_video_file(self, video_path: str) -> Dict[str, Any]:
        """تحليل ملف فيديو شامل"""
        try:
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / fps
            
            logger.info(f"تحليل فيديو: {duration:.1f}s، {fps:.1f} fps، {total_frames} إطار")
            
            # تحليل شامل
            global_features = self._extract_global_visual_features(cap, fps, total_frames)
            windowed_features = self._extract_windowed_visual_features(cap, fps, duration)
            highlights = self._detect_visual_highlights(windowed_features)
            
            # تحليل متقدم للمشاهد
            scene_analysis = self._analyze_scenes(windowed_features)
            
            # كشف أنماط بصرية خاصة
            special_patterns = self._detect_special_visual_patterns(windowed_features)
            
            cap.release()
            
            return {
                'duration': duration,
                'fps': fps,
                'total_frames': total_frames,
                'global_features': global_features,
                'windowed_features': windowed_features,
                'highlights': highlights,
                'scene_analysis': scene_analysis,
                'special_patterns': special_patterns,
                'quality_metrics': self._calculate_visual_quality(windowed_features)
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الملف المرئي: {e}")
            return self._analyze_video_basic(video_path)

    def _extract_global_visual_features(self, cap: cv2.VideoCapture, fps: float, total_frames: int) -> Dict[str, Any]:
        """استخراج الخصائص البصرية العامة"""
        try:
            features = {
                'avg_brightness': 0.0,
                'avg_contrast': 0.0,
                'avg_motion': 0.0,
                'total_faces_detected': 0,
                'scene_changes': 0,
                'dominant_colors': [],
                'resolution': (int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)), int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)))
            }
            
            # عينة من الإطارات للتحليل السريع
            sample_frames = min(100, total_frames // 10)  # عينة من 100 إطار أو 10% من الفيديو
            frame_step = max(1, total_frames // sample_frames)
            
            brightness_values = []
            contrast_values = []
            motion_values = []
            all_colors = []
            prev_frame = None
            
            for i in range(0, total_frames, frame_step):
                cap.set(cv2.CAP_PROP_POS_FRAMES, i)
                ret, frame = cap.read()
                if not ret:
                    break
                
                # تحليل الإطار
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                
                # السطوع
                brightness = np.mean(gray) / 255.0
                brightness_values.append(brightness)
                
                # التباين
                contrast = np.std(gray) / 255.0
                contrast_values.append(contrast)
                
                # الحركة (إذا كان هناك إطار سابق)
                if prev_frame is not None:
                    diff = cv2.absdiff(gray, prev_frame)
                    motion = np.mean(diff) / 255.0
                    motion_values.append(motion)
                
                # الألوان المهيمنة (عينة صغيرة)
                if len(all_colors) < 10:
                    colors = self._extract_dominant_colors(frame, k=3)
                    all_colors.extend(colors)
                
                # كشف الوجوه (عينة أصغر)
                if i % (frame_step * 5) == 0:  # كل 5 إطارات من العينة
                    faces = self._detect_faces_basic(frame)
                    features['total_faces_detected'] += len(faces)
                
                prev_frame = gray.copy()
            
            # حساب المتوسطات
            if brightness_values:
                features['avg_brightness'] = statistics.mean(brightness_values)
            if contrast_values:
                features['avg_contrast'] = statistics.mean(contrast_values)
            if motion_values:
                features['avg_motion'] = statistics.mean(motion_values)
            
            # الألوان المهيمنة
            if all_colors:
                features['dominant_colors'] = self._get_most_common_colors(all_colors)
            
            # تقدير تغييرات المشاهد
            if motion_values:
                high_motion_frames = sum(1 for m in motion_values if m > 0.5)
                features['scene_changes'] = high_motion_frames
            
            return features
            
        except Exception as e:
            logger.error(f"خطأ في استخراج الخصائص العامة: {e}")
            return {}

    def _extract_windowed_visual_features(self, cap: cv2.VideoCapture, fps: float, duration: float) -> List[VisualFeatures]:
        """استخراج خصائص النوافذ الزمنية"""
        try:
            features_list = []
            
            window_frames = int(self.window_size * fps)
            overlap_frames = int(self.overlap * fps)
            
            frame_count = 0
            current_time = 0
            
            while current_time < duration:
                # تحليل النافذة الحالية
                window_features = self._analyze_window(cap, frame_count, window_frames, fps, current_time)
                if window_features:
                    features_list.append(window_features)
                
                # الانتقال للنافذة التالية
                frame_count += overlap_frames
                current_time = frame_count / fps
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_count)
            
            return features_list
            
        except Exception as e:
            logger.error(f"خطأ في استخراج خصائص النوافذ: {e}")
            return []

    def _analyze_window(self, cap: cv2.VideoCapture, start_frame: int, window_frames: int, 
                       fps: float, timestamp: float) -> Optional[VisualFeatures]:
        """تحليل نافذة زمنية واحدة"""
        try:
            # جمع إطارات النافذة
            frames = []
            cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
            
            for i in range(0, window_frames, self.frame_skip):
                ret, frame = cap.read()
                if not ret:
                    break
                frames.append(frame)
            
            if not frames:
                return None
            
            # تحليل الإطارات
            motion_values = []
            brightness_values = []
            contrast_values = []
            edge_densities = []
            all_faces = []
            all_poses = []
            all_hands = []
            all_colors = []
            
            prev_gray = None
            
            for frame in frames:
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                
                # السطوع والتباين
                brightness = np.mean(gray) / 255.0
                contrast = np.std(gray) / 255.0
                brightness_values.append(brightness)
                contrast_values.append(contrast)
                
                # كثافة الحواف
                edges = cv2.Canny(gray, 50, 150)
                edge_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
                edge_densities.append(edge_density)
                
                # الحركة
                if prev_gray is not None:
                    diff = cv2.absdiff(gray, prev_gray)
                    motion = np.mean(diff) / 255.0
                    motion_values.append(motion)
                
                # كشف الوجوه والأجسام (عينة من الإطارات)
                if len(all_faces) < 3:  # تحليل 3 إطارات فقط لتوفير الوقت
                    faces = self._detect_faces_advanced(frame) if MEDIAPIPE_AVAILABLE else self._detect_faces_basic(frame)
                    all_faces.extend(faces)
                    
                    if MEDIAPIPE_AVAILABLE:
                        poses = self._detect_poses(frame)
                        hands = self._detect_hands(frame)
                        all_poses.extend(poses)
                        all_hands.extend(hands)
                
                # الألوان (عينة صغيرة)
                if len(all_colors) < 5:
                    colors = self._extract_dominant_colors(frame, k=2)
                    all_colors.extend(colors)
                
                prev_gray = gray.copy()
            
            # حساب الخصائص المجمعة
            motion_intensity = statistics.mean(motion_values) if motion_values else 0.0
            brightness = statistics.mean(brightness_values)
            contrast = statistics.mean(contrast_values)
            edge_density = statistics.mean(edge_densities)
            
            # تحليل الوجوه
            face_count = len(all_faces)
            face_emotions = self._analyze_face_emotions(all_faces) if all_faces else {}
            
            # تحليل الأجسام والإيماءات
            pose_detected = len(all_poses) > 0
            hand_gestures = self._analyze_hand_gestures(all_hands) if all_hands else []
            
            # تعقيد المشهد
            scene_complexity = self._calculate_scene_complexity(
                motion_intensity, edge_density, face_count, len(all_colors)
            )
            
            # الألوان المهيمنة
            dominant_colors = self._get_most_common_colors(all_colors) if all_colors else []
            
            # تباين الألوان
            color_variance = self._calculate_color_variance(frames[0]) if frames else 0.0
            
            return VisualFeatures(
                timestamp=timestamp,
                motion_intensity=motion_intensity,
                brightness=brightness,
                contrast=contrast,
                color_variance=color_variance,
                edge_density=edge_density,
                face_count=face_count,
                face_emotions=face_emotions,
                pose_detected=pose_detected,
                hand_gestures=hand_gestures,
                scene_complexity=scene_complexity,
                dominant_colors=dominant_colors
            )
            
        except Exception as e:
            logger.error(f"خطأ في تحليل النافذة: {e}")
            return None

    def _detect_faces_advanced(self, frame: np.ndarray) -> List[Dict[str, Any]]:
        """كشف الوجوه المتقدم باستخدام MediaPipe"""
        try:
            if not self.face_detection:
                return []

            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.face_detection.process(rgb_frame)

            faces = []
            if results.detections:
                for detection in results.detections:
                    bbox = detection.location_data.relative_bounding_box
                    confidence = detection.score[0]

                    faces.append({
                        'bbox': (bbox.xmin, bbox.ymin, bbox.width, bbox.height),
                        'confidence': confidence,
                        'landmarks': None  # يمكن إضافة landmarks لاحقاً
                    })

            return faces

        except Exception as e:
            logger.error(f"خطأ في كشف الوجوه المتقدم: {e}")
            return []

    def _detect_faces_basic(self, frame: np.ndarray) -> List[Dict[str, Any]]:
        """كشف الوجوه الأساسي باستخدام OpenCV"""
        try:
            if not hasattr(self, 'face_cascade') or self.face_cascade is None:
                return []

            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            faces = self.face_cascade.detectMultiScale(gray, 1.1, 4)

            face_list = []
            h, w = frame.shape[:2]

            for (x, y, w_face, h_face) in faces:
                # تحويل إلى إحداثيات نسبية
                face_list.append({
                    'bbox': (x/w, y/h, w_face/w, h_face/h),
                    'confidence': 0.8,  # ثقة افتراضية
                    'landmarks': None
                })

            return face_list

        except Exception as e:
            logger.error(f"خطأ في كشف الوجوه الأساسي: {e}")
            return []

    def _detect_poses(self, frame: np.ndarray) -> List[Dict[str, Any]]:
        """كشف الأجسام والوضعيات"""
        try:
            if not self.pose:
                return []

            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.pose.process(rgb_frame)

            poses = []
            if results.pose_landmarks:
                # استخراج نقاط مهمة
                landmarks = []
                for landmark in results.pose_landmarks.landmark:
                    landmarks.append({
                        'x': landmark.x,
                        'y': landmark.y,
                        'z': landmark.z,
                        'visibility': landmark.visibility
                    })

                poses.append({
                    'landmarks': landmarks,
                    'confidence': np.mean([lm['visibility'] for lm in landmarks])
                })

            return poses

        except Exception as e:
            logger.error(f"خطأ في كشف الأجسام: {e}")
            return []

    def _detect_hands(self, frame: np.ndarray) -> List[Dict[str, Any]]:
        """كشف الأيدي والإيماءات"""
        try:
            if not self.hands:
                return []

            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.hands.process(rgb_frame)

            hands = []
            if results.multi_hand_landmarks:
                for hand_landmarks in results.multi_hand_landmarks:
                    landmarks = []
                    for landmark in hand_landmarks.landmark:
                        landmarks.append({
                            'x': landmark.x,
                            'y': landmark.y,
                            'z': landmark.z
                        })

                    hands.append({
                        'landmarks': landmarks,
                        'handedness': 'unknown'  # يمكن تحسينه لاحقاً
                    })

            return hands

        except Exception as e:
            logger.error(f"خطأ في كشف الأيدي: {e}")
            return []

    def _extract_dominant_colors(self, frame: np.ndarray, k: int = 3) -> List[Tuple[int, int, int]]:
        """استخراج الألوان المهيمنة"""
        try:
            # تصغير الصورة لتسريع المعالجة
            small_frame = cv2.resize(frame, (50, 50))
            data = small_frame.reshape((-1, 3))
            data = np.float32(data)

            # استخدام K-means لتجميع الألوان
            criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 20, 1.0)
            _, labels, centers = cv2.kmeans(data, k, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS)

            # تحويل إلى ألوان RGB
            colors = []
            for center in centers:
                color = tuple(map(int, center))
                colors.append((color[2], color[1], color[0]))  # BGR to RGB

            return colors

        except Exception as e:
            logger.error(f"خطأ في استخراج الألوان المهيمنة: {e}")
            return []

    def _get_most_common_colors(self, all_colors: List[Tuple[int, int, int]]) -> List[Tuple[int, int, int]]:
        """الحصول على أكثر الألوان شيوعاً"""
        try:
            if not all_colors:
                return []

            # عد تكرار الألوان
            color_counts = {}
            for color in all_colors:
                # تقريب الألوان لتجميع الألوان المتشابهة
                rounded_color = tuple(round(c/10)*10 for c in color)
                color_counts[rounded_color] = color_counts.get(rounded_color, 0) + 1

            # ترتيب حسب التكرار
            sorted_colors = sorted(color_counts.items(), key=lambda x: x[1], reverse=True)

            # إرجاع أعلى 5 ألوان
            return [color for color, count in sorted_colors[:5]]

        except Exception as e:
            logger.error(f"خطأ في الحصول على الألوان الشائعة: {e}")
            return []

    def _calculate_color_variance(self, frame: np.ndarray) -> float:
        """حساب تباين الألوان"""
        try:
            # تحويل إلى HSV للحصول على تشبع أفضل
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)

            # حساب التباين في قناة التشبع
            saturation = hsv[:, :, 1]
            variance = np.var(saturation) / (255.0 ** 2)  # تطبيع

            return float(variance)

        except Exception as e:
            logger.error(f"خطأ في حساب تباين الألوان: {e}")
            return 0.0

    def _analyze_face_emotions(self, faces: List[Dict[str, Any]]) -> Dict[str, float]:
        """تحليل مشاعر الوجوه"""
        try:
            emotions = {
                'happiness': 0.0,
                'surprise': 0.0,
                'anger': 0.0,
                'sadness': 0.0,
                'neutral': 0.0
            }

            if not faces:
                emotions['neutral'] = 1.0
                return emotions

            # تحليل أساسي بناءً على عدد الوجوه وثقة الكشف
            avg_confidence = np.mean([face['confidence'] for face in faces])
            face_count = len(faces)

            # قواعد بسيطة للتقدير
            if face_count > 3:  # مجموعة كبيرة = احتمال فرح
                emotions['happiness'] = min(avg_confidence * 0.8, 1.0)
            elif face_count == 1:  # وجه واحد = محايد غالباً
                emotions['neutral'] = avg_confidence
            else:  # مجموعة صغيرة = متنوع
                emotions['happiness'] = avg_confidence * 0.6
                emotions['neutral'] = avg_confidence * 0.4

            return emotions

        except Exception as e:
            logger.error(f"خطأ في تحليل مشاعر الوجوه: {e}")
            return {'neutral': 1.0}

    def _analyze_hand_gestures(self, hands: List[Dict[str, Any]]) -> List[str]:
        """تحليل إيماءات الأيدي"""
        try:
            gestures = []

            for hand in hands:
                landmarks = hand.get('landmarks', [])
                if len(landmarks) >= 21:  # MediaPipe hand landmarks
                    # تحليل أساسي للإيماءات

                    # فحص الإشارة (إصبع مرفوع)
                    if self._is_pointing_gesture(landmarks):
                        gestures.append('pointing')

                    # فحص التصفيق (يدين قريبتين)
                    if len(hands) >= 2 and self._is_clapping_gesture(hands):
                        gestures.append('clapping')

                    # فحص رفع اليد
                    if self._is_raised_hand(landmarks):
                        gestures.append('raised_hand')

            return list(set(gestures))  # إزالة التكرار

        except Exception as e:
            logger.error(f"خطأ في تحليل إيماءات الأيدي: {e}")
            return []

    def _is_pointing_gesture(self, landmarks: List[Dict[str, Any]]) -> bool:
        """فحص إيماءة الإشارة"""
        try:
            # فحص بسيط: الإصبع السبابة مرفوع والباقي منخفض
            index_tip = landmarks[8]  # طرف السبابة
            index_pip = landmarks[6]  # مفصل السبابة
            middle_tip = landmarks[12]  # طرف الوسطى

            # السبابة مرفوعة والوسطى منخفضة
            return (index_tip['y'] < index_pip['y'] and
                   middle_tip['y'] > landmarks[10]['y'])

        except:
            return False

    def _is_clapping_gesture(self, hands: List[Dict[str, Any]]) -> bool:
        """فحص إيماءة التصفيق"""
        try:
            if len(hands) < 2:
                return False

            # حساب المسافة بين راحتي اليدين
            hand1_palm = hands[0]['landmarks'][9]  # وسط راحة اليد
            hand2_palm = hands[1]['landmarks'][9]

            distance = ((hand1_palm['x'] - hand2_palm['x'])**2 +
                       (hand1_palm['y'] - hand2_palm['y'])**2)**0.5

            # إذا كانت المسافة قصيرة = تصفيق محتمل
            return distance < 0.1

        except:
            return False

    def _is_raised_hand(self, landmarks: List[Dict[str, Any]]) -> bool:
        """فحص رفع اليد"""
        try:
            # فحص ما إذا كانت راحة اليد أعلى من المعصم
            palm_center = landmarks[9]  # وسط راحة اليد
            wrist = landmarks[0]  # المعصم

            return palm_center['y'] < wrist['y'] - 0.1

        except:
            return False

    def _calculate_scene_complexity(self, motion: float, edges: float,
                                  faces: int, colors: int) -> float:
        """حساب تعقيد المشهد"""
        try:
            # دمج عوامل مختلفة لحساب التعقيد
            complexity = 0.0

            # الحركة (25%)
            complexity += motion * 0.25

            # كثافة الحواف (25%)
            complexity += edges * 0.25

            # عدد الوجوه (25%)
            face_complexity = min(faces / 5.0, 1.0)  # تطبيع إلى 5 وجوه كحد أقصى
            complexity += face_complexity * 0.25

            # تنوع الألوان (25%)
            color_complexity = min(colors / 10.0, 1.0)  # تطبيع إلى 10 ألوان
            complexity += color_complexity * 0.25

            return min(complexity, 1.0)

        except Exception as e:
            logger.error(f"خطأ في حساب تعقيد المشهد: {e}")
            return 0.5

    def _detect_visual_highlights(self, features_list: List[VisualFeatures]) -> List[VisualHighlight]:
        """كشف اللحظات البصرية المميزة"""
        try:
            highlights = []

            if not features_list:
                return highlights

            # استخراج المتسلسلات الزمنية
            timestamps = [f.timestamp for f in features_list]
            motions = [f.motion_intensity for f in features_list]
            face_counts = [f.face_count for f in features_list]
            complexities = [f.scene_complexity for f in features_list]
            brightness_values = [f.brightness for f in features_list]

            # كشف ذروات الحركة
            motion_peaks = self._find_peaks_basic(motions, timestamps, self.motion_threshold)
            for peak_time, peak_value in motion_peaks:
                highlight = self._create_visual_highlight(
                    peak_time, peak_value, 'motion_peak', features_list
                )
                if highlight:
                    highlights.append(highlight)

            # كشف المشاهد الجماعية
            for features in features_list:
                if features.face_count >= self.face_count_threshold:
                    highlight = self._create_visual_highlight(
                        features.timestamp, features.face_count, 'crowd_scene', features_list
                    )
                    if highlight:
                        highlights.append(highlight)

            # كشف تغييرات الإضاءة
            brightness_changes = self._find_significant_changes_basic(
                brightness_values, timestamps, self.brightness_change_threshold
            )
            for change_time, change_value in brightness_changes:
                highlight = self._create_visual_highlight(
                    change_time, change_value, 'lighting_change', features_list
                )
                if highlight:
                    highlights.append(highlight)

            # كشف المشاهد المعقدة
            complexity_peaks = self._find_peaks_basic(complexities, timestamps, self.complexity_threshold)
            for peak_time, peak_value in complexity_peaks:
                highlight = self._create_visual_highlight(
                    peak_time, peak_value, 'action_sequence', features_list
                )
                if highlight:
                    highlights.append(highlight)

            # كشف الإيماءات
            for features in features_list:
                if features.hand_gestures:
                    highlight = self._create_visual_highlight(
                        features.timestamp, len(features.hand_gestures), 'gesture_moment', features_list
                    )
                    if highlight:
                        highlights.append(highlight)

            return highlights

        except Exception as e:
            logger.error(f"خطأ في كشف اللحظات البصرية: {e}")
            return []

    def _find_peaks_basic(self, values: List[float], timestamps: List[float],
                         threshold: float) -> List[Tuple[float, float]]:
        """كشف ذروات أساسي"""
        try:
            peaks = []

            for i in range(1, len(values) - 1):
                if (values[i] > values[i-1] and values[i] > values[i+1] and
                    values[i] >= threshold):
                    peaks.append((timestamps[i], values[i]))

            return peaks

        except Exception as e:
            logger.error(f"خطأ في كشف الذروات: {e}")
            return []

    def _find_significant_changes_basic(self, values: List[float], timestamps: List[float],
                                      threshold: float) -> List[Tuple[float, float]]:
        """العثور على تغييرات مهمة"""
        try:
            changes = []

            for i in range(1, len(values)):
                change = abs(values[i] - values[i-1])
                if change >= threshold:
                    changes.append((timestamps[i], change))

            return changes

        except Exception as e:
            logger.error(f"خطأ في العثور على التغييرات: {e}")
            return []

    def _create_visual_highlight(self, peak_time: float, peak_value: float,
                               highlight_type: str, features_list: List[VisualFeatures]) -> Optional[VisualHighlight]:
        """إنشاء لحظة بصرية مميزة"""
        try:
            # العثور على الخصائص المطابقة
            peak_features = None
            for features in features_list:
                if abs(features.timestamp - peak_time) < 0.5:
                    peak_features = features
                    break

            if not peak_features:
                return None

            # تحديد حدود اللحظة
            start_time = max(0, peak_time - 1.0)
            end_time = peak_time + 1.0

            # حساب الثقة والشدة
            confidence = self._calculate_visual_confidence(peak_features, peak_value, highlight_type)
            intensity_score = self._calculate_visual_intensity(peak_features, features_list)
            visual_impact = self._calculate_visual_impact(peak_features, highlight_type)

            return VisualHighlight(
                start_time=start_time,
                end_time=end_time,
                peak_time=peak_time,
                highlight_type=highlight_type,
                confidence=confidence,
                features=peak_features,
                intensity_score=intensity_score,
                visual_impact=visual_impact
            )

        except Exception as e:
            logger.error(f"خطأ في إنشاء اللحظة البصرية: {e}")
            return None

    def _calculate_visual_confidence(self, features: VisualFeatures, peak_value: float,
                                   highlight_type: str) -> float:
        """حساب ثقة اللحظة البصرية"""
        try:
            confidence = 0.5  # قيمة افتراضية

            if highlight_type == 'motion_peak':
                confidence = min(peak_value * 2, 1.0)
            elif highlight_type == 'crowd_scene':
                confidence = min(features.face_count / 5.0, 1.0)
            elif highlight_type == 'lighting_change':
                confidence = min(peak_value * 5, 1.0)
            elif highlight_type == 'action_sequence':
                confidence = features.scene_complexity
            elif highlight_type == 'gesture_moment':
                confidence = min(len(features.hand_gestures) / 3.0, 1.0)

            # تعديل بناءً على جودة الكشف
            if features.face_count > 0:
                confidence += 0.1
            if features.scene_complexity > 0.5:
                confidence += 0.1

            return min(confidence, 1.0)

        except Exception as e:
            logger.error(f"خطأ في حساب الثقة البصرية: {e}")
            return 0.5

    def _calculate_visual_intensity(self, features: VisualFeatures,
                                  all_features: List[VisualFeatures]) -> float:
        """حساب شدة اللحظة البصرية"""
        try:
            # مقارنة مع المتوسط العام
            avg_motion = np.mean([f.motion_intensity for f in all_features])
            avg_complexity = np.mean([f.scene_complexity for f in all_features])
            avg_faces = np.mean([f.face_count for f in all_features])

            # حساب النقاط النسبية
            motion_score = features.motion_intensity / max(avg_motion, 0.001)
            complexity_score = features.scene_complexity / max(avg_complexity, 0.001)
            face_score = features.face_count / max(avg_faces, 1)

            # دمج النقاط
            intensity = (motion_score * 0.4 + complexity_score * 0.4 + face_score * 0.2)
            return min(intensity, 2.0) / 2.0  # تطبيع إلى [0, 1]

        except Exception as e:
            logger.error(f"خطأ في حساب الشدة البصرية: {e}")
            return 0.5

    def _calculate_visual_impact(self, features: VisualFeatures, highlight_type: str) -> float:
        """حساب التأثير البصري"""
        try:
            impact = 0.5  # قيمة افتراضية

            # تحليل بناءً على نوع اللحظة
            if highlight_type == 'motion_peak':
                impact = min(features.motion_intensity * 2, 1.0)
            elif highlight_type == 'crowd_scene':
                impact = min(features.face_count / 3.0, 1.0)
            elif highlight_type == 'lighting_change':
                impact = 0.7  # تغيير الإضاءة له تأثير متوسط
            elif highlight_type == 'action_sequence':
                impact = features.scene_complexity
            elif highlight_type == 'gesture_moment':
                impact = 0.8  # الإيماءات لها تأثير عالي

            # تعديل بناءً على الخصائص الإضافية
            if features.contrast > 0.5:  # تباين عالي
                impact += 0.1
            if features.color_variance > 0.3:  # تنوع ألوان
                impact += 0.1
            if features.edge_density > 0.4:  # تفاصيل كثيرة
                impact += 0.1

            return min(impact, 1.0)

        except Exception as e:
            logger.error(f"خطأ في حساب التأثير البصري: {e}")
            return 0.5

    def _analyze_scenes(self, features_list: List[VisualFeatures]) -> Dict[str, Any]:
        """تحليل المشاهد"""
        try:
            scenes = {
                'scene_changes': 0,
                'average_complexity': 0.0,
                'dominant_scene_type': 'unknown',
                'face_density': 0.0,
                'motion_patterns': [],
                'color_transitions': []
            }

            if not features_list:
                return scenes

            # حساب تغييرات المشاهد
            complexities = [f.scene_complexity for f in features_list]
            scene_changes = 0
            for i in range(1, len(complexities)):
                if abs(complexities[i] - complexities[i-1]) > 0.3:
                    scene_changes += 1

            scenes['scene_changes'] = scene_changes
            scenes['average_complexity'] = statistics.mean(complexities)

            # كثافة الوجوه
            total_faces = sum(f.face_count for f in features_list)
            scenes['face_density'] = total_faces / len(features_list)

            # تحديد نوع المشهد المهيمن
            avg_faces = scenes['face_density']
            avg_motion = statistics.mean([f.motion_intensity for f in features_list])

            if avg_faces > 2:
                scenes['dominant_scene_type'] = 'crowd_scene'
            elif avg_motion > 0.5:
                scenes['dominant_scene_type'] = 'action_scene'
            elif scenes['average_complexity'] > 0.6:
                scenes['dominant_scene_type'] = 'complex_scene'
            else:
                scenes['dominant_scene_type'] = 'simple_scene'

            # أنماط الحركة
            motion_values = [f.motion_intensity for f in features_list]
            if motion_values:
                scenes['motion_patterns'] = [
                    {'time': f.timestamp, 'motion': f.motion_intensity}
                    for f in features_list
                ]

            return scenes

        except Exception as e:
            logger.error(f"خطأ في تحليل المشاهد: {e}")
            return {}

    def _detect_special_visual_patterns(self, features_list: List[VisualFeatures]) -> Dict[str, Any]:
        """كشف أنماط بصرية خاصة"""
        try:
            patterns = {
                'close_ups_detected': 0,
                'wide_shots_detected': 0,
                'color_bursts_detected': 0,
                'gesture_sequences': [],
                'face_reaction_moments': [],
                'lighting_effects': []
            }

            for features in features_list:
                # كشف اللقطات القريبة (وجه واحد كبير)
                if features.face_count == 1 and features.scene_complexity < 0.3:
                    patterns['close_ups_detected'] += 1

                # كشف اللقطات الواسعة (وجوه متعددة أو تعقيد عالي)
                if features.face_count > 3 or features.scene_complexity > 0.7:
                    patterns['wide_shots_detected'] += 1

                # كشف انفجار الألوان (تباين ألوان عالي)
                if features.color_variance > 0.5:
                    patterns['color_bursts_detected'] += 1
                    patterns['lighting_effects'].append({
                        'time': features.timestamp,
                        'type': 'color_burst',
                        'intensity': features.color_variance
                    })

                # كشف تسلسل الإيماءات
                if features.hand_gestures:
                    patterns['gesture_sequences'].append({
                        'time': features.timestamp,
                        'gestures': features.hand_gestures
                    })

                # كشف لحظات ردة فعل الوجه
                if features.face_count > 0 and any(v > 0.6 for v in features.face_emotions.values()):
                    patterns['face_reaction_moments'].append({
                        'time': features.timestamp,
                        'emotions': features.face_emotions,
                        'face_count': features.face_count
                    })

                # كشف تأثيرات الإضاءة (تغييرات سطوع كبيرة)
                if features.brightness > 0.8 or features.brightness < 0.2:
                    patterns['lighting_effects'].append({
                        'time': features.timestamp,
                        'type': 'extreme_lighting',
                        'brightness': features.brightness
                    })

            return patterns

        except Exception as e:
            logger.error(f"خطأ في كشف الأنماط الخاصة: {e}")
            return {}

    def _calculate_visual_quality(self, features_list: List[VisualFeatures]) -> Dict[str, float]:
        """حساب مقاييس الجودة البصرية"""
        try:
            quality = {}

            if not features_list:
                return quality

            # متوسط التباين (جودة الصورة)
            contrasts = [f.contrast for f in features_list]
            quality['average_contrast'] = statistics.mean(contrasts)
            quality['contrast_consistency'] = 1.0 - (statistics.stdev(contrasts) / max(statistics.mean(contrasts), 0.001))

            # استقرار السطوع
            brightness_values = [f.brightness for f in features_list]
            quality['brightness_stability'] = 1.0 - (statistics.stdev(brightness_values) / max(statistics.mean(brightness_values), 0.001))

            # ثراء التفاصيل
            edge_densities = [f.edge_density for f in features_list]
            quality['detail_richness'] = statistics.mean(edge_densities)

            # تنوع المحتوى
            complexities = [f.scene_complexity for f in features_list]
            quality['content_variety'] = statistics.stdev(complexities) if len(complexities) > 1 else 0

            # جودة كشف الوجوه
            face_counts = [f.face_count for f in features_list]
            quality['face_detection_rate'] = sum(1 for fc in face_counts if fc > 0) / len(face_counts)

            return quality

        except Exception as e:
            logger.error(f"خطأ في حساب الجودة البصرية: {e}")
            return {}

    def _analyze_video_basic(self, video_path: str) -> Dict[str, Any]:
        """تحليل فيديو أساسي بدون MediaPipe"""
        try:
            return {
                'duration': 60.0,
                'fps': 30.0,
                'total_frames': 1800,
                'global_features': {'avg_brightness': 0.5, 'avg_motion': 0.3},
                'windowed_features': [],
                'highlights': [],
                'scene_analysis': {'dominant_scene_type': 'simple_scene'},
                'special_patterns': {'close_ups_detected': 0},
                'quality_metrics': {'average_contrast': 0.5}
            }
        except Exception as e:
            logger.error(f"خطأ في التحليل الأساسي: {e}")
            return {}

# دوال مساعدة للتوافق مع الأداة الحالية
def analyze_video_advanced(video_path: str) -> Dict[str, Any]:
    """دالة مبسطة لتحليل الفيديو المتقدم"""
    try:
        analyzer = AdvancedVisualAnalyzer()
        return analyzer.analyze_video_file(video_path)
    except Exception as e:
        logger.error(f"خطأ في تحليل الفيديو المتقدم: {e}")
        return {}

def extract_visual_highlights(video_path: str) -> List[Dict[str, Any]]:
    """استخراج اللحظات البصرية المميزة"""
    try:
        analysis = analyze_video_advanced(video_path)
        highlights = analysis.get('highlights', [])
        
        # تحويل إلى تنسيق مبسط
        simple_highlights = []
        for highlight in highlights:
            simple_highlights.append({
                'start_time': highlight.start_time,
                'end_time': highlight.end_time,
                'type': highlight.highlight_type,
                'confidence': highlight.confidence,
                'intensity': highlight.intensity_score
            })
        
        return simple_highlights
        
    except Exception as e:
        logger.error(f"خطأ في استخراج اللحظات البصرية: {e}")
        return []

if __name__ == "__main__":
    # اختبار النظام
    print("🧪 اختبار محلل البصريات المتقدم")
    print("=" * 50)
    
    analyzer = AdvancedVisualAnalyzer()
    print(f"✅ تم تهيئة المحلل")
    print(f"📊 MediaPipe متوفر: {'نعم' if MEDIAPIPE_AVAILABLE else 'لا'}")
    print(f"🎬 أنواع اللحظات البصرية: {len(analyzer.visual_highlight_types)}")
    
    # عرض أنواع اللحظات البصرية
    print("\n🎥 أنواع اللحظات البصرية المدعومة:")
    for key, value in analyzer.visual_highlight_types.items():
        print(f"   - {key}: {value}")
    
    print(f"\n⚙️ إعدادات التحليل:")
    print(f"   - حجم النافذة: {analyzer.window_size}s")
    print(f"   - التداخل: {analyzer.overlap}s")
    print(f"   - تخطي الإطارات: {analyzer.frame_skip}")
    
    print("\n🏁 انتهى الاختبار")
