#!/usr/bin/env python3
"""
مكون التعامل مع Gemini API لاستخراج المقاطع المميزة - محسن مع إدارة مفاتيح متقدمة
Enhanced Gemini AI Integration for Highlight Extraction with Advanced Key Management
"""

import os
import json
import time
import logging
import random
from pathlib import Path
import google.generativeai as genai
from dotenv import load_dotenv
from typing import List, Dict, Tuple, Optional

# تحميل متغيرات البيئة
load_dotenv()

logger = logging.getLogger(__name__)

class AdvancedGeminiKeyManager:
    """مدير مفاتيح Gemini متقدم مع التبديل التلقائي"""

    def __init__(self):
        """تهيئة مدير المفاتيح المتقدم"""

        # قائمة شاملة بمفاتيح Gemini (محدثة من الأداة المتقدمة)
        self.api_keys = [
            # المفاتيح الجديدة (أولوية عالية)
            "AIzaSyC7Z3wBae-3J47R-LaPGnJGTrC22SvDg5M",
            "AIzaSyCQJ2CwE6_oSsBvzUs3TnaiHccXkjtgcWc",
            "AIzaSyBomvlphVTvUleoxzbJM8boL1KvkPuUZVc",
            "AIzaSyBlHvj5vAYWFD_Qs2RPlu5WBUIoPi4WuYQ",
            "AIzaSyArno9YCJbb7WfUG5llZgve644RIVRrntw",
            "AIzaSyBNR0YlKBy7UHf_9Gwd7hUtax41bqwAqX4",
            "AIzaSyA52Gg_lI3DN8sv0HykxPhLsmEYpCj6qlU",
            "AIzaSyBn2jVshv_jn60Yi_-UUmw9P31mDIs4w74",
            "AIzaSyAYgcYKr8ROUQjZdZwV9mn7K41gOtcsu1c",
            "AIzaSyDry4JMnQJwyGncKF9va6OC0LAX8b48WHs",
            "AIzaSyDFFCW_iKzlKrxEe4QARSzCL2nw0qsSrh8",
            "AIzaSyD8HVd2wuBDRnGDrKpobj_G2xRAHI6Z-88",
            "AIzaSyCwKaFunKbgO5FE8uEgqxbWgab8LcwBzvc",
            "AIzaSyBiMajBlgp6rC6tTvkAUa6s34EG4VeYZxk",
            "AIzaSyBSbT3AFUoZ4TulCuSgx8QLWp4N2qGe4vg",
            "AIzaSyDskR5hdZtbk8JOo4XjqZaj0F0i5bsJUYg",

            # المفاتيح الاحتياطية
            "AIzaSyDtfQhkANpIM-cHRy-UdMhHfROzSjBX5SY",  # المفتاح المفضل للمستخدم
            "AIzaSyD7E2B9iYXV9_2houLLHDWXUA-K53kUGq0",
            "AIzaSyBY58cZFvFzRQpzzYJ7m1VjwvS7af-vHhM",
            "AIzaSyBSbEdSARy5ims96kxF1om2725VZxwl6nU",
            "AIzaSyBx2D9UdlvuSCH4Z7jaT16S0MREkNIuVNc",
            "AIzaSyAuI06np4vmdKkWN1JucexLW1mO0ESEyts",
            "AIzaSyC4_fX42vOZYfuF56i_lNSJjiEs02vX3Uo",
            "AIzaSyDZfiLXBVs8yBk0CDb4hvLZ_l8P6tKy6og",
            "AIzaSyC_3oOs766IXUlWFdwUSNTVAhg_GLFfb1E"
        ]

        # إزالة المفاتيح المكررة
        self.api_keys = list(set(self.api_keys))

        # ملف تتبع حالة المفاتيح
        self.status_file = Path("data/gemini_keys_status.json")
        self.status_file.parent.mkdir(exist_ok=True)

        # تحميل حالة المفاتيح
        self.keys_status = self._load_keys_status()

        # المفتاح الحالي
        self.current_key_index = 0
        self.current_key = self._get_next_available_key()

        logger.info(f"تم تهيئة مدير مفاتيح Gemini المتقدم مع {len(self.api_keys)} مفتاح")

    def _load_keys_status(self) -> Dict[str, any]:
        """تحميل حالة المفاتيح من الملف"""
        try:
            if self.status_file.exists():
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    loaded_status = json.load(f)

                    # التأكد من وجود جميع المفاتيح في الحالة المحملة
                    for key in self.api_keys:
                        if key not in loaded_status:
                            loaded_status[key] = {"blocked": False, "last_used": 0, "requests_today": 0}

                    return loaded_status
            else:
                # إنشاء حالة افتراضية
                return {key: {"blocked": False, "last_used": 0, "requests_today": 0}
                       for key in self.api_keys}
        except Exception as e:
            logger.error(f"خطأ في تحميل حالة المفاتيح: {str(e)[:100]}")
            return {key: {"blocked": False, "last_used": 0, "requests_today": 0}
                   for key in self.api_keys}

    def _save_keys_status(self):
        """حفظ حالة المفاتيح في الملف"""
        try:
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(self.keys_status, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"خطأ في حفظ حالة المفاتيح: {e}")

    def _get_next_available_key(self) -> Optional[str]:
        """الحصول على المفتاح التالي المتاح"""
        current_time = time.time()

        # فحص جميع المفاتيح للعثور على مفتاح متاح
        available_keys = []
        recently_unblocked = []

        for i, key in enumerate(self.api_keys):
            key_status = self.keys_status.get(key, {})

            # إعادة تعيين المفاتيح المحظورة بعد 24 ساعة
            if key_status.get("blocked", False):
                last_used = key_status.get("last_used", 0)
                hours_passed = (current_time - last_used) / 3600

                if hours_passed > 24:  # 24 ساعة
                    self.keys_status[key]["blocked"] = False
                    self.keys_status[key]["requests_today"] = 0
                    recently_unblocked.append((key, i))
                    logger.info(f"تم إعادة تفعيل المفتاح: {key[-10:]} (بعد {hours_passed:.1f} ساعة)")

            # إذا كان المفتاح غير محظور
            if not self.keys_status[key].get("blocked", False):
                available_keys.append((key, i))

        # إعطاء أولوية للمفاتيح المُعاد تفعيلها حديثاً
        if recently_unblocked:
            key, index = recently_unblocked[0]
            self.current_key_index = index
            self._save_keys_status()
            return key

        # إذا كان هناك مفاتيح متاحة أخرى
        if available_keys:
            key, index = available_keys[0]
            self.current_key_index = index
            return key

        logger.warning("جميع مفاتيح Gemini محظورة مؤقتاً")
        return None

    def get_current_key(self) -> Optional[str]:
        """الحصول على المفتاح الحالي"""
        return self.current_key

    def mark_key_as_blocked(self, key: str, error_message: str = ""):
        """تمييز مفتاح كمحظور"""
        if key in self.keys_status:
            self.keys_status[key]["blocked"] = True
            self.keys_status[key]["last_used"] = time.time()
            self.keys_status[key]["error"] = error_message
            self._save_keys_status()

            logger.warning(f"تم حظر المفتاح: {key[-10:]} - {error_message}")

            # الانتقال للمفتاح التالي
            self.current_key = self._get_next_available_key()
            if self.current_key:
                logger.info(f"تم التبديل للمفتاح: {self.current_key[-10:]}")

    def is_quota_exceeded_error(self, error_message: str) -> bool:
        """فحص ما إذا كان الخطأ بسبب تجاوز الحد اليومي"""
        quota_errors = [
            "quota", "rate limit", "exceeded", "RESOURCE_EXHAUSTED",
            "429", "too many requests", "daily limit"
        ]

        error_lower = error_message.lower()
        return any(error in error_lower for error in quota_errors)

    def handle_api_error(self, key: str, error_message: str) -> bool:
        """التعامل مع خطأ API وإرجاع True إذا تم التبديل للمفتاح التالي"""
        if self.is_quota_exceeded_error(error_message):
            self.mark_key_as_blocked(key, error_message)
            return self.current_key is not None

        return False

    def get_available_keys_count(self) -> int:
        """الحصول على عدد المفاتيح المتاحة"""
        current_time = time.time()
        available_count = 0

        for key in self.api_keys:
            key_status = self.keys_status.get(key, {})

            if not key_status.get("blocked", False):
                available_count += 1
            else:
                # فحص ما إذا كان يمكن إعادة تفعيله
                last_used = key_status.get("last_used", 0)
                if current_time - last_used > 24 * 3600:
                    available_count += 1

        return available_count

    def record_successful_request(self, key: str):
        """تسجيل طلب ناجح"""
        if key in self.keys_status:
            self.keys_status[key]["requests_today"] = self.keys_status[key].get("requests_today", 0) + 1
            self.keys_status[key]["last_used"] = time.time()
            self._save_keys_status()

class GeminiHighlightExtractor:
    """فئة لاستخراج المقاطع المميزة باستخدام Gemini AI مع إدارة متقدمة"""

    def __init__(self):
        self.key_manager = AdvancedGeminiKeyManager()
        self.model = None
        self._initialize_gemini()

    def _initialize_gemini(self):
        """تهيئة Gemini API مع إدارة المفاتيح المتقدمة"""
        try:
            current_key = self.key_manager.get_current_key()
            if not current_key:
                logger.warning("لا توجد مفاتيح Gemini متاحة حالياً")
                self.model = None
                return

            # تكوين Gemini
            genai.configure(api_key=current_key)

            # إنشاء النموذج - استخدام أحدث نسخة Gemini 2.0
            self.model = genai.GenerativeModel('gemini-2.0-flash-exp')

            logger.info(f"✅ تم تهيئة Gemini API بنجاح مع المفتاح: {current_key[-10:]}")

        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة Gemini API: {e}")
            self.model = None
    
    def test_connection(self) -> Tuple[bool, str]:
        """اختبار الاتصال بـ Gemini API مع إدارة المفاتيح المتقدمة"""
        try:
            if not self.model:
                return False, "Gemini API غير مهيأ - لا توجد مفاتيح متاحة"

            current_key = self.key_manager.get_current_key()
            if not current_key:
                return False, "لا توجد مفاتيح Gemini متاحة حالياً"

            # اختبار بسيط
            response = self.model.generate_content("مرحبا")

            if response and response.text:
                # تسجيل طلب ناجح
                self.key_manager.record_successful_request(current_key)
                available_keys = self.key_manager.get_available_keys_count()
                return True, f"Gemini API يعمل بشكل صحيح ({available_keys}/{len(self.key_manager.api_keys)} مفتاح متاح)"
            else:
                return False, "لم يتم الحصول على رد من Gemini API"

        except Exception as e:
            error_msg = str(e)
            current_key = self.key_manager.get_current_key()

            # محاولة التعامل مع الخطأ والتبديل للمفتاح التالي
            if current_key and self.key_manager.handle_api_error(current_key, error_msg):
                # تم التبديل للمفتاح التالي، محاولة إعادة التهيئة
                self._initialize_gemini()
                return False, f"تم التبديل لمفتاح جديد بسبب: {error_msg[:50]}..."

            return False, f"خطأ في الاتصال بـ Gemini API: {error_msg}"


    
    def extract_highlights(self, transcript: str, max_duration: int = 60) -> List[Dict]:
        """
        استخراج المقاطع المميزة من النص المنسوخ مع إدارة المفاتيح المتقدمة

        Args:
            transcript: النص المنسوخ من الفيديو
            max_duration: الحد الأقصى لطول المقطع بالثواني

        Returns:
            قائمة بالمقاطع المميزة
        """
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                if not self.model:
                    logger.warning("Gemini API غير متاح، استخدام المقاطع الافتراضية")
                    return self._get_fallback_highlights()

                current_key = self.key_manager.get_current_key()
                if not current_key:
                    logger.warning("لا توجد مفاتيح Gemini متاحة، استخدام المقاطع الافتراضية")
                    return self._get_fallback_highlights()

                # إنشاء prompt للذكاء الاصطناعي
                prompt = self._create_highlight_prompt(transcript, max_duration)

                # إرسال الطلب لـ Gemini مع إعدادات محسنة
                generation_config = genai.types.GenerationConfig(
                    temperature=0.7,  # توازن بين الإبداع والدقة
                    top_p=0.8,
                    top_k=40,
                    max_output_tokens=2048,
                    response_mime_type="application/json"  # فرض إرجاع JSON
                )

                response = self.model.generate_content(
                    prompt,
                    generation_config=generation_config
                )

                if not response or not response.text:
                    logger.warning("لم يتم الحصول على رد من Gemini، استخدام المقاطع الافتراضية")
                    return self._get_fallback_highlights()

                # تسجيل طلب ناجح
                self.key_manager.record_successful_request(current_key)

                # تحليل الرد
                highlights = self._parse_gemini_response(response.text)

                if highlights:
                    logger.info(f"✅ تم استخراج {len(highlights)} مقطع مميز باستخدام Gemini (المفتاح: {current_key[-10:]})")
                    return highlights
                else:
                    logger.warning("فشل في تحليل رد Gemini، استخدام المقاطع الافتراضية")
                    return self._get_fallback_highlights()

            except Exception as e:
                error_msg = str(e)
                logger.error(f"خطأ في استخراج المقاطع باستخدام Gemini: {error_msg}")

                # محاولة التعامل مع الخطأ والتبديل للمفتاح التالي
                if current_key and self.key_manager.handle_api_error(current_key, error_msg):
                    logger.info(f"تم التبديل لمفتاح جديد، محاولة {retry_count + 1}/{max_retries}")
                    self._initialize_gemini()  # إعادة تهيئة مع المفتاح الجديد
                    retry_count += 1
                    continue
                else:
                    logger.error("لا توجد مفاتيح أخرى متاحة، استخدام المقاطع الافتراضية")
                    return self._get_fallback_highlights()

        logger.error("فشل في جميع المحاولات، استخدام المقاطع الافتراضية")
        return self._get_fallback_highlights()
    
    def _create_highlight_prompt(self, transcript: str, max_duration: int) -> str:
        """إنشاء prompt محسن لاستخراج المقاطع المميزة باستخدام Gemini 2.0"""
        prompt = f"""
أنت خبير متقدم في تحليل محتوى الفيديوهات باستخدام الذكاء الاصطناعي. مهمتك هي استخراج أفضل المقاطع المميزة لإنشاء فيديوهات قصيرة فيرالية وجذابة.

## التحليل المطلوب:
حلل النص المنسوخ التالي واستخرج 3-5 مقاطع مميزة بناءً على:

### معايير الاختيار:
1. **الجاذبية**: محتوى يجذب الانتباه في الثواني الأولى
2. **القيمة**: معلومات مفيدة أو مثيرة للاهتمام
3. **الفيرالية**: قابلية المشاركة والانتشار
4. **الوضوح**: رسالة واضحة ومفهومة
5. **التفاعل**: محتوى يحفز التعليقات والمشاركة

### القيود التقنية:
- مدة كل مقطع: {max_duration} ثانية كحد أقصى
- تجنب المقاطع المملة أو التكرارية
- اختر مقاطع متنوعة (بداية، وسط، نهاية)
- تأكد من اكتمال الفكرة في كل مقطع

### النص المنسوخ:
{transcript}

## المطلوب:
أرجع النتيجة بصيغة JSON صحيحة فقط، بدون أي نص إضافي أو تفسير:

```json
[
  {{
    "start": "وقت البداية بالثواني (رقم صحيح)",
    "end": "وقت النهاية بالثواني (رقم صحيح)",
    "title": "عنوان جذاب للمقطع",
    "description": "وصف مختصر يوضح محتوى المقطع",
    "reason": "سبب اختيار هذا المقطع (جاذبية، قيمة، فيرالية)",
    "engagement_score": "درجة من 1-10 للتفاعل المتوقع"
  }}
]
```

تذكر: اختر المقاطع التي ستحصل على أعلى مشاهدات ومشاركات على منصات التواصل الاجتماعي.
"""
        return prompt
    
    def _parse_gemini_response(self, response_text: str) -> List[Dict]:
        """تحليل رد Gemini واستخراج المقاطع"""
        try:
            # تنظيف النص
            cleaned_text = response_text.strip()
            
            # البحث عن JSON في النص
            start_idx = cleaned_text.find('[')
            end_idx = cleaned_text.rfind(']') + 1
            
            if start_idx == -1 or end_idx == 0:
                return []
            
            json_text = cleaned_text[start_idx:end_idx]
            
            # تحليل JSON
            highlights_data = json.loads(json_text)
            
            # تحويل إلى التنسيق المطلوب
            highlights = []
            for item in highlights_data:
                highlight = {
                    "start": self._parse_time(item.get('start', '0')),
                    "end": self._parse_time(item.get('end', '30')),
                    "text": item.get('title', 'مقطع مميز'),
                    "description": item.get('description', ''),
                    "reason": item.get('reason', ''),
                    "engagement_score": item.get('engagement_score', '8')
                }
                highlights.append(highlight)
            
            return highlights
            
        except json.JSONDecodeError as e:
            print(f"خطأ في تحليل JSON من Gemini: {e}")
            return []
        except Exception as e:
            print(f"خطأ في تحليل رد Gemini: {e}")
            return []
    
    def _parse_time(self, time_str: str) -> str:
        """تحويل الوقت إلى تنسيق MM:SS"""
        try:
            # إذا كان الوقت بالثواني
            if isinstance(time_str, (int, float)):
                seconds = int(time_str)
            else:
                seconds = int(float(str(time_str)))
            
            minutes = seconds // 60
            seconds = seconds % 60
            return f"{minutes:02d}:{seconds:02d}"
            
        except:
            return "00:00"
    
    def _get_fallback_highlights(self) -> List[Dict]:
        """مقاطع افتراضية محسنة في حالة فشل Gemini"""
        return [
            {
                "start": "00:10",
                "end": "00:40",
                "text": "🔥 مقطع افتتاحي قوي",
                "description": "بداية جذابة تلفت الانتباه",
                "reason": "محتوى جذاب يحفز المشاهدة",
                "engagement_score": "8"
            },
            {
                "start": "01:30",
                "end": "02:00",
                "text": "💡 نقطة مهمة ومفيدة",
                "description": "معلومة قيمة في وسط الفيديو",
                "reason": "محتوى تعليمي مفيد",
                "engagement_score": "7"
            },
            {
                "start": "02:45",
                "end": "03:15",
                "text": "✨ خاتمة مؤثرة",
                "description": "استنتاج قوي يترك أثراً",
                "reason": "رسالة ملهمة للنهاية",
                "engagement_score": "9"
            }
        ]

# إنشاء مثيل عام (lazy loading) مع إدارة المفاتيح المتقدمة
gemini_extractor = None

def get_gemini_extractor():
    """الحصول على مثيل Gemini Extractor مع lazy loading وإدارة المفاتيح المتقدمة"""
    global gemini_extractor
    if gemini_extractor is None:
        gemini_extractor = GeminiHighlightExtractor()
    return gemini_extractor

def GetHighlightWithGemini(transcript: str) -> List[Dict]:
    """
    دالة مبسطة لاستخراج المقاطع المميزة باستخدام Gemini مع إدارة المفاتيح المتقدمة

    Args:
        transcript: النص المنسوخ

    Returns:
        قائمة بالمقاطع المميزة
    """
    try:
        extractor = get_gemini_extractor()
        return extractor.extract_highlights(transcript)
    except Exception as e:
        logger.error(f"خطأ في GetHighlightWithGemini: {e}")
        # إرجاع مقاطع افتراضية في حالة الخطأ
        return [
            {
                "start": "00:10",
                "end": "00:40",
                "text": "🔥 مقطع مثير للاهتمام",
                "description": "تم إنشاؤه تلقائياً بسبب خطأ في Gemini",
                "reason": "fallback",
                "engagement_score": "7"
            }
        ]

def get_gemini_status() -> Dict[str, any]:
    """الحصول على حالة مفاتيح Gemini"""
    try:
        extractor = get_gemini_extractor()
        if extractor and extractor.key_manager:
            available_keys = extractor.key_manager.get_available_keys_count()
            total_keys = len(extractor.key_manager.api_keys)
            current_key = extractor.key_manager.get_current_key()

            return {
                "available_keys": available_keys,
                "total_keys": total_keys,
                "current_key_suffix": current_key[-10:] if current_key else "لا يوجد",
                "status": "متاح" if current_key else "غير متاح"
            }
        else:
            return {
                "available_keys": 0,
                "total_keys": 0,
                "current_key_suffix": "لا يوجد",
                "status": "غير مهيأ"
            }
    except Exception as e:
        logger.error(f"خطأ في الحصول على حالة Gemini: {e}")
        return {
            "available_keys": 0,
            "total_keys": 0,
            "current_key_suffix": "خطأ",
            "status": "خطأ"
        }

# للتوافق مع الكود القديم
def GetHighlight(transcript: str) -> Tuple[int, int]:
    """
    دالة للتوافق مع الكود القديم - ترجع أول مقطع مميز
    """
    highlights = GetHighlightWithGemini(transcript)
    
    if highlights:
        first_highlight = highlights[0]
        start_time = _time_to_seconds(first_highlight['start'])
        end_time = _time_to_seconds(first_highlight['end'])
        return start_time, end_time
    
    return 10, 40  # قيم افتراضية

def _time_to_seconds(time_str: str) -> int:
    """تحويل وقت MM:SS إلى ثواني"""
    try:
        parts = time_str.split(':')
        if len(parts) == 2:
            minutes, seconds = map(int, parts)
            return minutes * 60 + seconds
        return int(time_str)
    except:
        return 0

if __name__ == "__main__":
    # إنشاء مجلد البيانات إذا لم يكن موجوداً
    Path("data").mkdir(exist_ok=True)

    # اختبار المكون المحسن
    print("🧪 اختبار Gemini Highlight Extractor المحسن")
    print("=" * 60)

    # اختبار الاتصال
    extractor = get_gemini_extractor()
    success, message = extractor.test_connection()
    print(f"🔗 اختبار الاتصال: {message}")

    # عرض حالة المفاتيح
    status = get_gemini_status()
    print(f"📊 حالة المفاتيح: {status['available_keys']}/{status['total_keys']} متاح")
    print(f"🔑 المفتاح الحالي: {status['current_key_suffix']}")
    print(f"📈 الحالة: {status['status']}")
    print("-" * 60)

    if success:
        # اختبار استخراج المقاطع
        test_transcript = """
        مرحباً بكم في هذا الفيديو الرائع! سنتحدث اليوم عن موضوع مهم جداً
        وهو كيفية استخدام الذكاء الاصطناعي في إنشاء مقاطع فيديو قصيرة.
        هذه التقنية ستغير طريقة إنتاج المحتوى بشكل كبير!
        OMG هذا مذهل! لا أصدق كم هو سهل الآن!
        """

        print("🎬 اختبار استخراج المقاطع المميزة...")
        highlights = GetHighlightWithGemini(test_transcript)
        print(f"✅ تم استخراج {len(highlights)} مقطع مميز")
        print("-" * 60)

        for i, highlight in enumerate(highlights, 1):
            print(f"{i}. ⏰ {highlight['start']} - {highlight['end']}")
            print(f"   📝 {highlight['text']}")
            print(f"   📋 {highlight.get('description', 'لا يوجد وصف')}")
            print(f"   🎯 نقاط التفاعل: {highlight.get('engagement_score', 'غير محدد')}")
            print()
    else:
        print("❌ فشل في الاتصال بـ Gemini API")
        print("🔄 جاري استخدام المقاطع الافتراضية...")

        # اختبار المقاطع الافتراضية
        fallback_highlights = extractor._get_fallback_highlights()
        print(f"📦 تم إنشاء {len(fallback_highlights)} مقطع افتراضي")

        for i, highlight in enumerate(fallback_highlights, 1):
            print(f"{i}. {highlight['start']} - {highlight['end']}: {highlight['text']}")

    print("=" * 60)
    print("🏁 انتهى الاختبار")
