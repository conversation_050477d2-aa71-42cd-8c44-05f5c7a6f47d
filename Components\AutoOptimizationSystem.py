#!/usr/bin/env python3
"""
نظام التحسين التلقائي
Auto Optimization System

يحسن اللقطات تلقائياً باستخدام كشف ردات الفعل والتحليل الذكي
مدمج من الأداة المتقدمة مع تحسينات للأداة الحالية
"""

import os
import json
import time
import logging
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import statistics
from collections import defaultdict
import cv2

# استيراد المحللات المتقدمة
try:
    from .IntelligentAnalysisEngine import IntelligentAnalysisEngine, IntelligentHighlight, ContentAnalysis
    from .AdvancedAudioAnalyzer import AdvancedAudioAnalyzer
    from .AdvancedVisualAnalyzer import AdvancedVisualAnalyzer
    from .CrowdDetectionSystem import CrowdDetectionSystem
except ImportError:
    # في حالة التشغيل المباشر
    import sys
    sys.path.append('.')
    from IntelligentAnalysisEngine import IntelligentAnalysisEngine, IntelligentHighlight, ContentAnalysis
    from AdvancedAudioAnalyzer import AdvancedAudioAnalyzer
    from AdvancedVisualAnalyzer import AdvancedVisualAnalyzer
    from CrowdDetectionSystem import CrowdDetectionSystem

logger = logging.getLogger(__name__)

@dataclass
class OptimizedClip:
    """مقطع محسن"""
    start_time: float
    end_time: float
    duration: float
    optimization_score: float
    optimization_reasons: List[str]
    highlight_features: Dict[str, Any]
    recommended_effects: List[str]
    viral_potential: float
    engagement_score: float

@dataclass
class OptimizationResult:
    """نتيجة التحسين"""
    original_duration: float
    optimized_clips: List[OptimizedClip]
    total_optimized_duration: float
    improvement_score: float
    optimization_summary: Dict[str, Any]
    recommendations: List[str]

class AutoOptimizationSystem:
    """نظام التحسين التلقائي"""
    
    def __init__(self):
        # تهيئة محرك التحليل الذكي
        self.analysis_engine = IntelligentAnalysisEngine()
        
        # إعدادات التحسين
        self.optimization_settings = {
            'min_clip_duration': 3.0,      # أقل مدة للمقطع (ثواني)
            'max_clip_duration': 15.0,     # أقصى مدة للمقطع (ثواني)
            'optimal_clip_duration': 8.0,  # المدة المثلى للمقطع
            'overlap_tolerance': 1.0,      # تداخل مسموح بين المقاطع
            'quality_threshold': 0.6,      # عتبة الجودة المطلوبة
            'viral_threshold': 0.7,        # عتبة الفيروسية
            'engagement_threshold': 0.6    # عتبة التفاعل
        }
        
        # استراتيجيات التحسين
        self.optimization_strategies = {
            'reaction_based': self._optimize_by_reactions,
            'highlight_based': self._optimize_by_highlights,
            'engagement_based': self._optimize_by_engagement,
            'viral_based': self._optimize_by_viral_potential,
            'balanced': self._optimize_balanced
        }
        
        # قواعد التحسين
        self.optimization_rules = self._load_optimization_rules()
        
        # إحصائيات التحسين
        self.optimization_stats = {
            'total_optimizations': 0,
            'average_improvement': 0.0,
            'successful_optimizations': 0
        }
        
        logger.info("تم تهيئة نظام التحسين التلقائي")

    def optimize_content_automatically(self, video_path: str, audio_path: str = None, 
                                     text_content: str = None, timestamps: List[float] = None,
                                     strategy: str = 'balanced') -> OptimizationResult:
        """تحسين المحتوى تلقائياً"""
        try:
            logger.info(f"بدء التحسين التلقائي باستراتيجية: {strategy}")
            
            # تحليل ذكي شامل
            content_analysis = self.analysis_engine.analyze_content_intelligently(
                video_path, audio_path, text_content, timestamps
            )
            
            # الحصول على مدة الفيديو الأصلية
            original_duration = self._get_video_duration(video_path)
            
            # تطبيق استراتيجية التحسين
            if strategy in self.optimization_strategies:
                optimized_clips = self.optimization_strategies[strategy](content_analysis, original_duration)
            else:
                logger.warning(f"استراتيجية غير معروفة: {strategy}، استخدام الاستراتيجية المتوازنة")
                optimized_clips = self.optimization_strategies['balanced'](content_analysis, original_duration)
            
            # تحسين المقاطع
            refined_clips = self._refine_clips(optimized_clips, content_analysis)
            
            # حساب نقاط التحسين
            optimization_result = self._create_optimization_result(
                original_duration, refined_clips, content_analysis
            )
            
            # تحديث الإحصائيات
            self._update_optimization_stats(optimization_result)
            
            logger.info(f"تم إنشاء {len(refined_clips)} مقطع محسن")
            return optimization_result
            
        except Exception as e:
            logger.error(f"خطأ في التحسين التلقائي: {e}")
            return self._get_empty_optimization_result()

    def _optimize_by_reactions(self, analysis: ContentAnalysis, duration: float) -> List[OptimizedClip]:
        """تحسين بناءً على ردات الفعل"""
        try:
            clips = []
            
            # البحث عن اللحظات مع ردات فعل قوية
            reaction_highlights = [
                h for h in analysis.highlights 
                if 'crowd' in h.multi_modal_features or 'reaction' in h.highlight_type.lower()
            ]
            
            for highlight in reaction_highlights:
                # توسيع المقطع حول ردة الفعل
                extended_clip = self._extend_around_highlight(highlight, duration)
                
                if extended_clip:
                    clips.append(extended_clip)
            
            return clips
            
        except Exception as e:
            logger.error(f"خطأ في التحسين بناءً على ردات الفعل: {e}")
            return []

    def _optimize_by_highlights(self, analysis: ContentAnalysis, duration: float) -> List[OptimizedClip]:
        """تحسين بناءً على اللحظات المميزة"""
        try:
            clips = []
            
            # ترتيب اللحظات حسب النقاط المدمجة
            sorted_highlights = sorted(
                analysis.highlights,
                key=lambda h: (h.viral_score + h.engagement_score + h.emotional_impact) / 3,
                reverse=True
            )
            
            # أخذ أفضل اللحظات
            top_highlights = sorted_highlights[:5]  # أفضل 5 لحظات
            
            for highlight in top_highlights:
                clip = self._create_clip_from_highlight(highlight, duration)
                if clip:
                    clips.append(clip)
            
            return clips
            
        except Exception as e:
            logger.error(f"خطأ في التحسين بناءً على اللحظات: {e}")
            return []

    def _optimize_by_engagement(self, analysis: ContentAnalysis, duration: float) -> List[OptimizedClip]:
        """تحسين بناءً على التفاعل"""
        try:
            clips = []
            
            # البحث عن اللحظات عالية التفاعل
            high_engagement = [
                h for h in analysis.highlights 
                if h.engagement_score >= self.optimization_settings['engagement_threshold']
            ]
            
            for highlight in high_engagement:
                clip = self._create_engagement_focused_clip(highlight, duration)
                if clip:
                    clips.append(clip)
            
            return clips
            
        except Exception as e:
            logger.error(f"خطأ في التحسين بناءً على التفاعل: {e}")
            return []

    def _optimize_by_viral_potential(self, analysis: ContentAnalysis, duration: float) -> List[OptimizedClip]:
        """تحسين بناءً على الإمكانية الفيروسية"""
        try:
            clips = []
            
            # البحث عن اللحظات عالية الفيروسية
            viral_highlights = [
                h for h in analysis.highlights 
                if h.viral_score >= self.optimization_settings['viral_threshold']
            ]
            
            for highlight in viral_highlights:
                clip = self._create_viral_focused_clip(highlight, duration)
                if clip:
                    clips.append(clip)
            
            return clips
            
        except Exception as e:
            logger.error(f"خطأ في التحسين بناءً على الفيروسية: {e}")
            return []

    def _optimize_balanced(self, analysis: ContentAnalysis, duration: float) -> List[OptimizedClip]:
        """تحسين متوازن"""
        try:
            clips = []
            
            # تطبيق جميع الاستراتيجيات وأخذ الأفضل
            all_clips = []
            
            # جمع المقاطع من جميع الاستراتيجيات
            reaction_clips = self._optimize_by_reactions(analysis, duration)
            highlight_clips = self._optimize_by_highlights(analysis, duration)
            engagement_clips = self._optimize_by_engagement(analysis, duration)
            viral_clips = self._optimize_by_viral_potential(analysis, duration)
            
            all_clips.extend(reaction_clips)
            all_clips.extend(highlight_clips)
            all_clips.extend(engagement_clips)
            all_clips.extend(viral_clips)
            
            # إزالة التكرار والتداخل
            unique_clips = self._remove_overlapping_clips(all_clips)
            
            # ترتيب حسب نقاط التحسين
            sorted_clips = sorted(unique_clips, key=lambda c: c.optimization_score, reverse=True)
            
            # أخذ أفضل المقاطع
            return sorted_clips[:3]  # أفضل 3 مقاطع
            
        except Exception as e:
            logger.error(f"خطأ في التحسين المتوازن: {e}")
            return []

    def _extend_around_highlight(self, highlight: IntelligentHighlight, total_duration: float) -> Optional[OptimizedClip]:
        """توسيع المقطع حول اللحظة المميزة"""
        try:
            # حساب التوسيع المطلوب
            highlight_duration = highlight.end_time - highlight.start_time
            target_duration = self.optimization_settings['optimal_clip_duration']
            
            if highlight_duration >= target_duration:
                # اللحظة طويلة بما فيه الكفاية
                start_time = highlight.start_time
                end_time = min(highlight.end_time, highlight.start_time + target_duration)
            else:
                # توسيع حول اللحظة
                extension_needed = target_duration - highlight_duration
                extension_before = extension_needed / 2
                extension_after = extension_needed / 2
                
                start_time = max(0, highlight.start_time - extension_before)
                end_time = min(total_duration, highlight.end_time + extension_after)
            
            # التأكد من الحد الأدنى للمدة
            actual_duration = end_time - start_time
            if actual_duration < self.optimization_settings['min_clip_duration']:
                return None
            
            # حساب نقاط التحسين
            optimization_score = self._calculate_optimization_score(highlight, actual_duration)
            
            # أسباب التحسين
            reasons = [
                f"لحظة مميزة من نوع: {highlight.highlight_type}",
                f"نقاط فيروسية: {highlight.viral_score:.2f}",
                f"نقاط تفاعل: {highlight.engagement_score:.2f}"
            ]
            
            # تأثيرات مقترحة
            effects = self._suggest_effects_for_highlight(highlight)
            
            return OptimizedClip(
                start_time=start_time,
                end_time=end_time,
                duration=actual_duration,
                optimization_score=optimization_score,
                optimization_reasons=reasons,
                highlight_features=asdict(highlight),
                recommended_effects=effects,
                viral_potential=highlight.viral_score,
                engagement_score=highlight.engagement_score
            )
            
        except Exception as e:
            logger.error(f"خطأ في توسيع المقطع: {e}")
            return None

    def _create_clip_from_highlight(self, highlight: IntelligentHighlight, total_duration: float) -> Optional[OptimizedClip]:
        """إنشاء مقطع من لحظة مميزة"""
        try:
            # استخدام حدود اللحظة مع تعديل طفيف
            start_time = max(0, highlight.start_time - 0.5)  # نصف ثانية قبل
            end_time = min(total_duration, highlight.end_time + 0.5)  # نصف ثانية بعد
            
            duration = end_time - start_time
            
            # التأكد من الحد الأدنى
            if duration < self.optimization_settings['min_clip_duration']:
                return None
            
            optimization_score = (highlight.viral_score + highlight.engagement_score + highlight.emotional_impact) / 3
            
            reasons = [
                f"لحظة مميزة: {highlight.highlight_type}",
                f"ثقة عالية: {highlight.confidence:.2f}"
            ]
            
            effects = self._suggest_effects_for_highlight(highlight)
            
            return OptimizedClip(
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                optimization_score=optimization_score,
                optimization_reasons=reasons,
                highlight_features=asdict(highlight),
                recommended_effects=effects,
                viral_potential=highlight.viral_score,
                engagement_score=highlight.engagement_score
            )
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء المقطع: {e}")
            return None

    def _create_engagement_focused_clip(self, highlight: IntelligentHighlight, total_duration: float) -> Optional[OptimizedClip]:
        """إنشاء مقطع مركز على التفاعل"""
        try:
            # توسيع أكبر للمقاطع عالية التفاعل
            extension = 2.0  # ثانيتين إضافيتين
            
            start_time = max(0, highlight.start_time - extension)
            end_time = min(total_duration, highlight.end_time + extension)
            
            duration = end_time - start_time
            
            if duration < self.optimization_settings['min_clip_duration']:
                return None
            
            # نقاط تحسين مركزة على التفاعل
            optimization_score = highlight.engagement_score * 1.2  # تعزيز نقاط التفاعل
            
            reasons = [
                f"تفاعل عالي: {highlight.engagement_score:.2f}",
                "مقطع موسع لزيادة التفاعل"
            ]
            
            effects = ["zoom_on_reactions", "highlight_text", "engagement_boost"]
            
            return OptimizedClip(
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                optimization_score=min(optimization_score, 1.0),
                optimization_reasons=reasons,
                highlight_features=asdict(highlight),
                recommended_effects=effects,
                viral_potential=highlight.viral_score,
                engagement_score=highlight.engagement_score
            )
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء مقطع التفاعل: {e}")
            return None

    def _create_viral_focused_clip(self, highlight: IntelligentHighlight, total_duration: float) -> Optional[OptimizedClip]:
        """إنشاء مقطع مركز على الفيروسية"""
        try:
            # مقاطع أقصر وأكثر تركيزاً للفيروسية
            target_duration = 6.0  # 6 ثوانٍ للمحتوى الفيروسي
            
            # التركيز على ذروة اللحظة
            peak_time = highlight.peak_time
            start_time = max(0, peak_time - target_duration / 2)
            end_time = min(total_duration, peak_time + target_duration / 2)
            
            duration = end_time - start_time
            
            if duration < self.optimization_settings['min_clip_duration']:
                return None
            
            # نقاط تحسين مركزة على الفيروسية
            optimization_score = highlight.viral_score * 1.3  # تعزيز نقاط الفيروسية
            
            reasons = [
                f"إمكانية فيروسية عالية: {highlight.viral_score:.2f}",
                "مقطع مركز على الذروة"
            ]
            
            effects = ["viral_effects", "quick_cuts", "trending_text"]
            
            return OptimizedClip(
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                optimization_score=min(optimization_score, 1.0),
                optimization_reasons=reasons,
                highlight_features=asdict(highlight),
                recommended_effects=effects,
                viral_potential=highlight.viral_score,
                engagement_score=highlight.engagement_score
            )
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء مقطع الفيروسية: {e}")
            return None

    def _remove_overlapping_clips(self, clips: List[OptimizedClip]) -> List[OptimizedClip]:
        """إزالة المقاطع المتداخلة"""
        try:
            if not clips:
                return []

            # ترتيب حسب وقت البداية
            sorted_clips = sorted(clips, key=lambda c: c.start_time)

            unique_clips = []
            tolerance = self.optimization_settings['overlap_tolerance']

            for clip in sorted_clips:
                # فحص التداخل مع المقاطع الموجودة
                overlaps = False
                for existing_clip in unique_clips:
                    if self._clips_overlap(clip, existing_clip, tolerance):
                        # إذا كان المقطع الجديد أفضل، استبدل القديم
                        if clip.optimization_score > existing_clip.optimization_score:
                            unique_clips.remove(existing_clip)
                            unique_clips.append(clip)
                        overlaps = True
                        break

                if not overlaps:
                    unique_clips.append(clip)

            return unique_clips

        except Exception as e:
            logger.error(f"خطأ في إزالة التداخل: {e}")
            return clips

    def _clips_overlap(self, clip1: OptimizedClip, clip2: OptimizedClip, tolerance: float) -> bool:
        """فحص تداخل المقاطع"""
        try:
            # حساب التداخل
            overlap_start = max(clip1.start_time, clip2.start_time)
            overlap_end = min(clip1.end_time, clip2.end_time)

            if overlap_end <= overlap_start:
                return False  # لا يوجد تداخل

            overlap_duration = overlap_end - overlap_start

            # إذا كان التداخل أكبر من التسامح
            return overlap_duration > tolerance

        except Exception as e:
            logger.error(f"خطأ في فحص التداخل: {e}")
            return False

    def _refine_clips(self, clips: List[OptimizedClip], analysis: ContentAnalysis) -> List[OptimizedClip]:
        """تحسين المقاطع"""
        try:
            refined_clips = []

            for clip in clips:
                # تطبيق قواعد التحسين
                refined_clip = self._apply_optimization_rules(clip, analysis)

                # فحص الجودة النهائية
                if self._meets_quality_standards(refined_clip):
                    refined_clips.append(refined_clip)

            return refined_clips

        except Exception as e:
            logger.error(f"خطأ في تحسين المقاطع: {e}")
            return clips

    def _apply_optimization_rules(self, clip: OptimizedClip, analysis: ContentAnalysis) -> OptimizedClip:
        """تطبيق قواعد التحسين"""
        try:
            # تطبيق القواعد المختلفة
            for rule in self.optimization_rules:
                if self._rule_applies(clip, rule):
                    clip = self._apply_rule(clip, rule)

            return clip

        except Exception as e:
            logger.error(f"خطأ في تطبيق قواعد التحسين: {e}")
            return clip

    def _meets_quality_standards(self, clip: OptimizedClip) -> bool:
        """فحص معايير الجودة"""
        try:
            # معايير الجودة
            min_score = self.optimization_settings['quality_threshold']
            min_duration = self.optimization_settings['min_clip_duration']
            max_duration = self.optimization_settings['max_clip_duration']

            return (clip.optimization_score >= min_score and
                   min_duration <= clip.duration <= max_duration)

        except Exception as e:
            logger.error(f"خطأ في فحص الجودة: {e}")
            return True

    def _calculate_optimization_score(self, highlight: IntelligentHighlight, duration: float) -> float:
        """حساب نقاط التحسين"""
        try:
            # نقاط أساسية من اللحظة
            base_score = (highlight.viral_score + highlight.engagement_score + highlight.emotional_impact) / 3

            # تعديل بناءً على المدة
            optimal_duration = self.optimization_settings['optimal_clip_duration']
            duration_factor = 1.0 - abs(duration - optimal_duration) / optimal_duration
            duration_factor = max(0.5, duration_factor)  # حد أدنى 0.5

            # تعديل بناءً على الثقة
            confidence_factor = highlight.confidence

            # تعديل بناءً على تعدد الوسائط
            multimodal_factor = 1.0 + (len(highlight.multi_modal_features) - 1) * 0.1

            # النقاط النهائية
            final_score = base_score * duration_factor * confidence_factor * multimodal_factor

            return min(final_score, 1.0)

        except Exception as e:
            logger.error(f"خطأ في حساب نقاط التحسين: {e}")
            return 0.5

    def _suggest_effects_for_highlight(self, highlight: IntelligentHighlight) -> List[str]:
        """اقتراح تأثيرات للحظة المميزة"""
        try:
            effects = []

            # تأثيرات بناءً على نوع اللحظة
            highlight_type = highlight.highlight_type.lower()

            if 'crowd' in highlight_type or 'applause' in highlight_type:
                effects.extend(['zoom_in', 'applause_sound_boost', 'crowd_highlight'])

            if 'viral' in highlight_type:
                effects.extend(['trending_text', 'viral_effects', 'share_prompt'])

            if 'emotional' in highlight_type:
                effects.extend(['emotional_music', 'slow_motion', 'emotional_text'])

            if 'engagement' in highlight_type:
                effects.extend(['call_to_action', 'engagement_prompt', 'interactive_elements'])

            # تأثيرات بناءً على النقاط
            if highlight.viral_score > 0.8:
                effects.append('viral_boost')

            if highlight.engagement_score > 0.8:
                effects.append('engagement_boost')

            if highlight.emotional_impact > 0.8:
                effects.append('emotional_boost')

            # تأثيرات بناءً على المصادر
            if 'audio' in highlight.multi_modal_features:
                effects.append('audio_highlight')

            if 'visual' in highlight.multi_modal_features:
                effects.append('visual_enhancement')

            if 'text' in highlight.multi_modal_features:
                effects.append('text_overlay')

            return list(set(effects))  # إزالة التكرار

        except Exception as e:
            logger.error(f"خطأ في اقتراح التأثيرات: {e}")
            return ['basic_enhancement']

    def _get_video_duration(self, video_path: str) -> float:
        """الحصول على مدة الفيديو"""
        try:
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
            duration = frame_count / fps if fps > 0 else 60.0  # افتراضي 60 ثانية
            cap.release()
            return duration

        except Exception as e:
            logger.error(f"خطأ في الحصول على مدة الفيديو: {e}")
            return 60.0  # مدة افتراضية

    def _create_optimization_result(self, original_duration: float, clips: List[OptimizedClip],
                                  analysis: ContentAnalysis) -> OptimizationResult:
        """إنشاء نتيجة التحسين"""
        try:
            # حساب المدة المحسنة الإجمالية
            total_optimized_duration = sum(clip.duration for clip in clips)

            # حساب نقاط التحسين
            if clips:
                avg_optimization_score = statistics.mean([clip.optimization_score for clip in clips])
                improvement_score = avg_optimization_score
            else:
                improvement_score = 0.0

            # ملخص التحسين
            optimization_summary = {
                'clips_created': len(clips),
                'duration_reduction': original_duration - total_optimized_duration,
                'average_clip_duration': total_optimized_duration / len(clips) if clips else 0,
                'best_clip_score': max([clip.optimization_score for clip in clips]) if clips else 0,
                'optimization_strategies_used': self._get_used_strategies(clips)
            }

            # التوصيات
            recommendations = self._generate_optimization_recommendations(clips, analysis, improvement_score)

            return OptimizationResult(
                original_duration=original_duration,
                optimized_clips=clips,
                total_optimized_duration=total_optimized_duration,
                improvement_score=improvement_score,
                optimization_summary=optimization_summary,
                recommendations=recommendations
            )

        except Exception as e:
            logger.error(f"خطأ في إنشاء نتيجة التحسين: {e}")
            return self._get_empty_optimization_result()

    def _get_used_strategies(self, clips: List[OptimizedClip]) -> List[str]:
        """الحصول على الاستراتيجيات المستخدمة"""
        try:
            strategies = set()

            for clip in clips:
                for reason in clip.optimization_reasons:
                    if 'ردة فعل' in reason or 'reaction' in reason.lower():
                        strategies.add('reaction_based')
                    elif 'فيروسية' in reason or 'viral' in reason.lower():
                        strategies.add('viral_based')
                    elif 'تفاعل' in reason or 'engagement' in reason.lower():
                        strategies.add('engagement_based')
                    else:
                        strategies.add('highlight_based')

            return list(strategies)

        except Exception as e:
            logger.error(f"خطأ في تحديد الاستراتيجيات: {e}")
            return ['unknown']

    def _generate_optimization_recommendations(self, clips: List[OptimizedClip],
                                             analysis: ContentAnalysis, improvement_score: float) -> List[str]:
        """إنشاء توصيات التحسين"""
        try:
            recommendations = []

            if improvement_score > 0.8:
                recommendations.append("تحسين ممتاز - المقاطع جاهزة للنشر")
            elif improvement_score > 0.6:
                recommendations.append("تحسين جيد - يمكن إضافة تأثيرات بسيطة")
            else:
                recommendations.append("يحتاج تحسين إضافي - فكر في إعادة التحليل")

            if len(clips) > 3:
                recommendations.append("عدد كبير من المقاطع - اختر الأفضل منها")
            elif len(clips) < 2:
                recommendations.append("عدد قليل من المقاطع - ابحث عن لحظات إضافية")

            # توصيات بناءً على التأثيرات
            all_effects = [effect for clip in clips for effect in clip.recommended_effects]
            common_effects = [effect for effect in set(all_effects) if all_effects.count(effect) > 1]

            if common_effects:
                recommendations.append(f"استخدم التأثيرات الشائعة: {', '.join(common_effects[:3])}")

            return recommendations

        except Exception as e:
            logger.error(f"خطأ في إنشاء التوصيات: {e}")
            return ["لا توجد توصيات محددة"]

    def _load_optimization_rules(self) -> List[Dict]:
        """تحميل قواعد التحسين"""
        try:
            return [
                {
                    'name': 'extend_high_viral',
                    'condition': lambda clip: clip.viral_potential > 0.8,
                    'action': 'extend_duration',
                    'parameter': 1.0
                },
                {
                    'name': 'shorten_low_engagement',
                    'condition': lambda clip: clip.engagement_score < 0.4,
                    'action': 'reduce_duration',
                    'parameter': 0.5
                },
                {
                    'name': 'boost_multimodal',
                    'condition': lambda clip: len(clip.highlight_features.get('multi_modal_features', {})) >= 3,
                    'action': 'boost_score',
                    'parameter': 0.1
                }
            ]
        except Exception as e:
            logger.error(f"خطأ في تحميل قواعد التحسين: {e}")
            return []

    def _rule_applies(self, clip: OptimizedClip, rule: Dict) -> bool:
        """فحص انطباق القاعدة"""
        try:
            condition = rule.get('condition')
            if callable(condition):
                return condition(clip)
            return False
        except Exception as e:
            logger.error(f"خطأ في فحص القاعدة: {e}")
            return False

    def _apply_rule(self, clip: OptimizedClip, rule: Dict) -> OptimizedClip:
        """تطبيق القاعدة"""
        try:
            action = rule.get('action')
            parameter = rule.get('parameter', 0)

            if action == 'extend_duration':
                clip.duration = min(clip.duration + parameter, self.optimization_settings['max_clip_duration'])
                clip.end_time = clip.start_time + clip.duration
            elif action == 'reduce_duration':
                clip.duration = max(clip.duration - parameter, self.optimization_settings['min_clip_duration'])
                clip.end_time = clip.start_time + clip.duration
            elif action == 'boost_score':
                clip.optimization_score = min(clip.optimization_score + parameter, 1.0)

            return clip

        except Exception as e:
            logger.error(f"خطأ في تطبيق القاعدة: {e}")
            return clip

    def _update_optimization_stats(self, result: OptimizationResult):
        """تحديث إحصائيات التحسين"""
        try:
            self.optimization_stats['total_optimizations'] += 1

            if result.improvement_score > 0.6:
                self.optimization_stats['successful_optimizations'] += 1

            # تحديث المتوسط
            current_avg = self.optimization_stats['average_improvement']
            total_opts = self.optimization_stats['total_optimizations']

            new_avg = ((current_avg * (total_opts - 1)) + result.improvement_score) / total_opts
            self.optimization_stats['average_improvement'] = new_avg

        except Exception as e:
            logger.error(f"خطأ في تحديث الإحصائيات: {e}")

    def _get_empty_optimization_result(self) -> OptimizationResult:
        """إرجاع نتيجة تحسين فارغة"""
        return OptimizationResult(
            original_duration=0.0,
            optimized_clips=[],
            total_optimized_duration=0.0,
            improvement_score=0.0,
            optimization_summary={},
            recommendations=["فشل في التحسين"]
        )

# دوال مساعدة للتوافق مع الأداة الحالية
def optimize_video_automatically(video_path: str, audio_path: str = None, 
                                text_content: str = None, strategy: str = 'balanced') -> Dict[str, Any]:
    """تحسين الفيديو تلقائياً"""
    try:
        optimizer = AutoOptimizationSystem()
        result = optimizer.optimize_content_automatically(video_path, audio_path, text_content, None, strategy)
        
        # تحويل إلى تنسيق مبسط
        return {
            'success': True,
            'original_duration': result.original_duration,
            'optimized_clips': [
                {
                    'start_time': clip.start_time,
                    'end_time': clip.end_time,
                    'duration': clip.duration,
                    'score': clip.optimization_score,
                    'reasons': clip.optimization_reasons,
                    'effects': clip.recommended_effects
                }
                for clip in result.optimized_clips
            ],
            'improvement_score': result.improvement_score,
            'recommendations': result.recommendations
        }
        
    except Exception as e:
        logger.error(f"خطأ في التحسين التلقائي: {e}")
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    # اختبار النظام
    print("🧪 اختبار نظام التحسين التلقائي")
    print("=" * 50)
    
    try:
        optimizer = AutoOptimizationSystem()
        print(f"✅ تم تهيئة النظام")
        print(f"⚙️ إعدادات التحسين:")
        for key, value in optimizer.optimization_settings.items():
            print(f"   - {key}: {value}")
        
        print(f"\n🎯 استراتيجيات التحسين المتاحة:")
        for strategy in optimizer.optimization_strategies.keys():
            print(f"   - {strategy}")
        
        print("\n🏁 انتهى الاختبار")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
