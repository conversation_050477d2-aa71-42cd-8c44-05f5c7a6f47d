#!/usr/bin/env python3
"""
كاشف ردات الفعل المتقدم
Advanced Reaction Detection System

يكتشف ردات فعل الأشخاص في الفيديو ويحدد التوقيت الأمثل للقطع
مدمج من الأداة المتقدمة مع تحسينات للأداة الحالية
"""

import cv2
import numpy as np
import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path
from collections import deque
import statistics
import time

# محاولة استيراد MediaPipe
try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
except ImportError:
    MEDIAPIPE_AVAILABLE = False
    mp = None

logger = logging.getLogger(__name__)

@dataclass
class ReactionMoment:
    """لحظة ردة فعل"""
    start_time: float
    peak_time: float
    end_time: float
    intensity: float
    emotion_type: str
    confidence: float
    face_changes: Dict[str, float]
    body_movement: float
    audio_changes: Dict[str, float]

class AdvancedReactionDetector:
    """كاشف ردات الفعل المتقدم"""
    
    def __init__(self):
        # إعداد MediaPipe للوجوه (إذا كان متوفراً)
        if MEDIAPIPE_AVAILABLE:
            self.mp_face_mesh = mp.solutions.face_mesh
            self.mp_pose = mp.solutions.pose
            self.mp_hands = mp.solutions.hands

            # إعداد كاشفات الوجه والجسم
            try:
                self.face_mesh = self.mp_face_mesh.FaceMesh(
                    static_image_mode=False,
                    max_num_faces=5,  # كشف حتى 5 وجوه للردات الجماعية
                    refine_landmarks=True,
                    min_detection_confidence=0.4,  # حساسية أعلى
                    min_tracking_confidence=0.4
                )

                self.pose = self.mp_pose.Pose(
                    static_image_mode=False,
                    model_complexity=1,
                    smooth_landmarks=True,
                    min_detection_confidence=0.5,
                    min_tracking_confidence=0.5
                )
                logger.info("تم تهيئة MediaPipe بنجاح")
            except Exception as e:
                logger.warning(f"فشل في تهيئة MediaPipe: {e}")
                self.face_mesh = None
                self.pose = None
        else:
            logger.warning("MediaPipe غير متوفر - سيتم استخدام كشف أساسي")
            self.mp_face_mesh = None
            self.mp_pose = None
            self.mp_hands = None
            self.face_mesh = None
            self.pose = None

            # إعداد كاشف وجه أساسي باستخدام OpenCV
            try:
                self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
                logger.info("تم تهيئة كاشف الوجه الأساسي (OpenCV)")
            except Exception as e:
                logger.warning(f"فشل في تهيئة كاشف الوجه الأساسي: {e}")
                self.face_cascade = None
        
        # إعدادات التحليل
        self.reaction_window = 15.0  # نافذة 15 ثانية للبحث عن ردة فعل
        self.min_reaction_duration = 0.5  # أقل مدة لردة فعل
        self.max_reaction_duration = 12.0  # أقصى مدة لردة فعل

        # عتبات الكشف
        self.emotion_threshold = 0.25  # أكثر حساسية للمشاعر
        self.movement_threshold = 0.3   # أكثر حساسية للحركة
        self.intensity_threshold = 0.4  # أكثر حساسية للشدة
        self.crowd_reaction_threshold = 0.6  # عتبة ردة فعل جماعية
        
        # تخزين البيانات المؤقتة
        self.face_history = deque(maxlen=30)  # آخر 30 إطار
        self.movement_history = deque(maxlen=30)
        self.emotion_history = deque(maxlen=30)
        self.crowd_history = deque(maxlen=30)  # تاريخ ردات الفعل الجماعية

        # أنواع ردات الفعل المختلفة
        self.reaction_types = {
            'individual_surprise': 'مفاجأة فردية',
            'individual_excitement': 'إثارة فردية',
            'individual_joy': 'فرح فردي',
            'individual_shock': 'صدمة فردية',
            'crowd_applause': 'تصفيق جماعي',
            'crowd_cheer': 'هتاف جماعي',
            'crowd_gasp': 'تنهد جماعي',
            'crowd_laughter': 'ضحك جماعي',
            'phone_recording': 'تسجيل بالهواتف',
            'pointing_gestures': 'إشارات وإيماءات',
            'jumping_celebration': 'قفز احتفالي',
            'standing_ovation': 'وقوف تصفيق'
        }

        logger.info("تم تهيئة كاشف ردات الفعل المتقدم")

    def detect_reactions_in_video(self, video_path: str, trigger_moments: List[Tuple[float, float]]) -> List[ReactionMoment]:
        """كشف ردات الفعل في الفيديو بعد اللحظات المحفزة"""
        try:
            reactions = []
            
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            for trigger_start, trigger_end in trigger_moments:
                logger.info(f"البحث عن ردة فعل بعد اللحظة: {trigger_start:.1f}-{trigger_end:.1f}s")
                
                # تحليل النافذة بعد اللحظة المحفزة
                reaction_start = trigger_end
                reaction_end = min(trigger_end + self.reaction_window, 
                                 cap.get(cv2.CAP_PROP_FRAME_COUNT) / fps)
                
                reaction = self._analyze_reaction_window(
                    cap, fps, reaction_start, reaction_end, trigger_start
                )
                
                if reaction:
                    reactions.append(reaction)
            
            cap.release()
            return reactions
            
        except Exception as e:
            logger.error(f"خطأ في كشف ردات الفعل: {e}")
            return []

    def _analyze_reaction_window(self, cap: cv2.VideoCapture, fps: float, 
                                start_time: float, end_time: float, 
                                trigger_time: float) -> Optional[ReactionMoment]:
        """تحليل نافذة زمنية للبحث عن ردة فعل"""
        try:
            # الانتقال لنقطة البداية
            start_frame = int(start_time * fps)
            cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
            
            # تخزين البيانات
            frame_data = []
            current_time = start_time
            
            while current_time < end_time:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # تحليل الإطار
                frame_analysis = self._analyze_frame(frame, current_time)
                if frame_analysis:
                    frame_data.append(frame_analysis)
                
                current_time = cap.get(cv2.CAP_PROP_POS_FRAMES) / fps
            
            # البحث عن ردة فعل في البيانات
            reaction = self._find_reaction_in_data(frame_data, trigger_time)
            return reaction
            
        except Exception as e:
            logger.error(f"خطأ في تحليل نافذة ردة الفعل: {e}")
            return None

    def _analyze_frame(self, frame: np.ndarray, timestamp: float) -> Optional[Dict[str, Any]]:
        """تحليل إطار واحد"""
        try:
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            analysis = {
                'timestamp': timestamp,
                'face_emotions': {},
                'body_movement': 0.0,
                'face_landmarks': None,
                'pose_landmarks': None,
                'crowd_reactions': {},
                'special_gestures': {},
                'multiple_faces': 0,
                'phone_detection': 0
            }
            
            # تحليل الوجه والمشاعر
            if MEDIAPIPE_AVAILABLE and self.face_mesh:
                face_results = self.face_mesh.process(rgb_frame)
                if face_results.multi_face_landmarks:
                    analysis['multiple_faces'] = len(face_results.multi_face_landmarks)

                    # تحليل كل وجه
                    all_emotions = {}
                    for i, face_landmarks in enumerate(face_results.multi_face_landmarks):
                        emotions = self._analyze_facial_emotions(face_landmarks, frame.shape)
                        # دمج المشاعر من جميع الوجوه
                        for emotion, value in emotions.items():
                            if emotion in all_emotions:
                                all_emotions[emotion] = max(all_emotions[emotion], value)
                            else:
                                all_emotions[emotion] = value

                    analysis['face_emotions'] = all_emotions
                    analysis['face_landmarks'] = face_results.multi_face_landmarks[0]  # الوجه الأول

                    # كشف ردات الفعل الجماعية
                    if analysis['multiple_faces'] >= 2:
                        crowd_reactions = self._analyze_crowd_reactions(face_results.multi_face_landmarks, frame.shape)
                        analysis['crowd_reactions'] = crowd_reactions
            else:
                # استخدام كشف وجه أساسي
                analysis.update(self._basic_face_detection(frame))
            
            # تحليل حركة الجسم
            if MEDIAPIPE_AVAILABLE and self.pose:
                pose_results = self.pose.process(rgb_frame)
                if pose_results.pose_landmarks:
                    movement = self._analyze_body_movement(pose_results.pose_landmarks)
                    analysis['body_movement'] = movement
                    analysis['pose_landmarks'] = pose_results.pose_landmarks

                    # تحليل الإيماءات الخاصة
                    special_gestures = self._analyze_special_gestures(pose_results.pose_landmarks)
                    analysis['special_gestures'] = special_gestures
            else:
                # تحليل حركة أساسي
                analysis['body_movement'] = self._basic_movement_detection(frame)
                analysis['special_gestures'] = {}

            # كشف الهواتف والتسجيل
            phone_detection = self._detect_phone_recording(rgb_frame)
            analysis['phone_detection'] = phone_detection
            
            return analysis
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الإطار: {e}")
            return None

    def extend_highlight_with_reaction(self, original_start: float, original_end: float,
                                     reactions: List[ReactionMoment]) -> Tuple[float, float]:
        """توسيع اللقطة الأصلية لتشمل ردة الفعل"""
        try:
            # البحث عن ردة فعل قريبة من نهاية اللقطة الأصلية
            best_reaction = None
            min_gap = float('inf')
            
            for reaction in reactions:
                gap = reaction.start_time - original_end
                if 0 <= gap <= 3.0 and gap < min_gap:  # ردة فعل خلال 3 ثوانٍ
                    best_reaction = reaction
                    min_gap = gap
            
            if best_reaction:
                # توسيع اللقطة لتشمل ردة الفعل
                new_end = best_reaction.end_time + 0.5  # إضافة نصف ثانية بعد انتهاء ردة الفعل
                logger.info(f"تم توسيع اللقطة من {original_end:.1f}s إلى {new_end:.1f}s لتشمل ردة الفعل")
                return original_start, new_end
            
            return original_start, original_end
            
        except Exception as e:
            logger.error(f"خطأ في توسيع اللقطة: {e}")
            return original_start, original_end

    def _analyze_facial_emotions(self, face_landmarks, frame_shape) -> Dict[str, float]:
        """تحليل المشاعر من ملامح الوجه"""
        try:
            emotions = {
                'surprise': 0.0,
                'joy': 0.0,
                'shock': 0.0,
                'excitement': 0.0,
                'confusion': 0.0
            }

            # تحويل النقاط إلى إحداثيات
            h, w = frame_shape[:2]
            landmarks = []
            for landmark in face_landmarks.landmark:
                x = int(landmark.x * w)
                y = int(landmark.y * h)
                landmarks.append([x, y])

            landmarks = np.array(landmarks)

            # تحليل العينين (نقاط محددة من MediaPipe)
            left_eye = landmarks[[33, 7, 163, 144, 145, 153]]
            right_eye = landmarks[[362, 382, 381, 380, 374, 373]]

            # حساب انفتاح العينين
            left_eye_openness = self._calculate_eye_openness(left_eye)
            right_eye_openness = self._calculate_eye_openness(right_eye)
            avg_eye_openness = (left_eye_openness + right_eye_openness) / 2

            # العيون المفتوحة جداً = مفاجأة/صدمة
            if avg_eye_openness > 0.7:
                emotions['surprise'] = min(avg_eye_openness, 1.0)
                emotions['shock'] = min(avg_eye_openness * 0.8, 1.0)

            # تحليل الفم
            mouth_landmarks = landmarks[[61, 84, 17, 314, 405, 320]]
            mouth_openness = self._calculate_mouth_openness(mouth_landmarks)

            # الفم المفتوح = مفاجأة/إثارة
            if mouth_openness > 0.5:
                emotions['surprise'] += mouth_openness * 0.6
                emotions['excitement'] = mouth_openness * 0.8

            # تحليل الحواجب
            left_eyebrow = landmarks[[70, 63, 105, 66, 107]]
            right_eyebrow = landmarks[[296, 334, 293, 300, 276]]
            eyebrow_raise = self._calculate_eyebrow_raise(left_eyebrow, right_eyebrow, landmarks)

            # الحواجب المرفوعة = مفاجأة
            if eyebrow_raise > 0.3:
                emotions['surprise'] += eyebrow_raise * 0.5

            # تطبيع القيم
            for emotion in emotions:
                emotions[emotion] = min(emotions[emotion], 1.0)

            return emotions

        except Exception as e:
            logger.error(f"خطأ في تحليل المشاعر: {e}")
            return {}

    def _calculate_eye_openness(self, eye_landmarks) -> float:
        """حساب مدى انفتاح العين"""
        try:
            # حساب المسافة العمودية بين الجفون
            vertical_dist = np.linalg.norm(eye_landmarks[1] - eye_landmarks[5])
            # حساب المسافة الأفقية
            horizontal_dist = np.linalg.norm(eye_landmarks[0] - eye_landmarks[3])

            # نسبة الانفتاح
            if horizontal_dist > 0:
                openness = vertical_dist / horizontal_dist
                return min(openness * 3, 1.0)  # تطبيع
            return 0.0
        except:
            return 0.0

    def _calculate_mouth_openness(self, mouth_landmarks) -> float:
        """حساب مدى انفتاح الفم"""
        try:
            # حساب المسافة بين الشفة العلوية والسفلية
            vertical_dist = np.linalg.norm(mouth_landmarks[1] - mouth_landmarks[4])
            # حساب عرض الفم
            horizontal_dist = np.linalg.norm(mouth_landmarks[0] - mouth_landmarks[3])

            if horizontal_dist > 0:
                openness = vertical_dist / horizontal_dist
                return min(openness * 2, 1.0)
            return 0.0
        except:
            return 0.0

    def _calculate_eyebrow_raise(self, left_eyebrow, right_eyebrow, all_landmarks) -> float:
        """حساب مدى رفع الحواجب"""
        try:
            # نقاط مرجعية للوجه
            nose_tip = all_landmarks[1]  # طرف الأنف

            # حساب متوسط ارتفاع الحواجب
            left_avg_y = np.mean(left_eyebrow[:, 1])
            right_avg_y = np.mean(right_eyebrow[:, 1])
            avg_eyebrow_y = (left_avg_y + right_avg_y) / 2

            # المسافة من الحواجب إلى الأنف (كلما قلت = حواجب مرفوعة)
            distance_to_nose = abs(avg_eyebrow_y - nose_tip[1])

            # تطبيع القيمة (قيمة أعلى = حواجب أكثر ارتفاعاً)
            normalized = max(0, (50 - distance_to_nose) / 50)
            return min(normalized, 1.0)
        except:
            return 0.0

    def _analyze_crowd_reactions(self, multi_face_landmarks, frame_shape) -> Dict[str, float]:
        """تحليل ردات الفعل الجماعية"""
        try:
            crowd_reactions = {
                'applause_probability': 0.0,
                'cheer_probability': 0.0,
                'gasp_probability': 0.0,
                'laughter_probability': 0.0
            }

            if len(multi_face_landmarks) < 2:
                return crowd_reactions

            # تحليل كل وجه
            individual_emotions = []
            for face_landmarks in multi_face_landmarks:
                emotions = self._analyze_facial_emotions(face_landmarks, frame_shape)
                individual_emotions.append(emotions)

            # حساب المتوسطات
            avg_surprise = np.mean([e.get('surprise', 0) for e in individual_emotions])
            avg_joy = np.mean([e.get('joy', 0) for e in individual_emotions])
            avg_excitement = np.mean([e.get('excitement', 0) for e in individual_emotions])

            # تحديد نوع ردة الفعل الجماعية
            if avg_surprise > 0.6:
                crowd_reactions['gasp_probability'] = avg_surprise

            if avg_joy > 0.5:
                crowd_reactions['laughter_probability'] = avg_joy

            if avg_excitement > 0.7:
                crowd_reactions['cheer_probability'] = avg_excitement
                crowd_reactions['applause_probability'] = avg_excitement * 0.8

            return crowd_reactions

        except Exception as e:
            logger.error(f"خطأ في تحليل ردات الفعل الجماعية: {e}")
            return {}

    def _analyze_body_movement(self, pose_landmarks) -> float:
        """تحليل حركة الجسم"""
        try:
            # استخراج نقاط مهمة
            landmarks = []
            for landmark in pose_landmarks.landmark:
                landmarks.append([landmark.x, landmark.y, landmark.z])

            landmarks = np.array(landmarks)

            # حساب الحركة بناءً على تغيير المواضع
            if len(self.movement_history) > 0:
                prev_landmarks = self.movement_history[-1]
                movement = np.mean(np.linalg.norm(landmarks - prev_landmarks, axis=1))
            else:
                movement = 0.0

            # حفظ في التاريخ
            self.movement_history.append(landmarks)

            return min(movement * 10, 1.0)  # تطبيع

        except Exception as e:
            logger.error(f"خطأ في تحليل حركة الجسم: {e}")
            return 0.0

    def _analyze_special_gestures(self, pose_landmarks) -> Dict[str, float]:
        """تحليل الإيماءات الخاصة"""
        try:
            gestures = {
                'arms_raised': 0.0,
                'pointing': 0.0,
                'jumping': 0.0,
                'clapping': 0.0
            }

            landmarks = []
            for landmark in pose_landmarks.landmark:
                landmarks.append([landmark.x, landmark.y, landmark.z])

            landmarks = np.array(landmarks)

            # كشف رفع الذراعين
            left_wrist = landmarks[15]  # معصم يسار
            right_wrist = landmarks[16]  # معصم يمين
            left_shoulder = landmarks[11]  # كتف يسار
            right_shoulder = landmarks[12]  # كتف يمين

            # إذا كانت المعاصم أعلى من الأكتاف
            if left_wrist[1] < left_shoulder[1] and right_wrist[1] < right_shoulder[1]:
                gestures['arms_raised'] = 0.8

            # كشف القفز (تغيير في موضع الوركين)
            left_hip = landmarks[23]
            right_hip = landmarks[24]
            avg_hip_y = (left_hip[1] + right_hip[1]) / 2

            if len(self.movement_history) > 5:
                prev_hip_positions = [h[23:25, 1].mean() for h in self.movement_history[-5:]]
                if avg_hip_y < min(prev_hip_positions) - 0.05:  # قفز للأعلى
                    gestures['jumping'] = 0.9

            return gestures

        except Exception as e:
            logger.error(f"خطأ في تحليل الإيماءات الخاصة: {e}")
            return {}

    def _detect_phone_recording(self, rgb_frame) -> float:
        """كشف الأشخاص الذين يسجلون بهواتفهم"""
        try:
            # كشف أساسي للأشكال المستطيلة الصغيرة (هواتف)
            gray = cv2.cvtColor(rgb_frame, cv2.COLOR_RGB2GRAY)

            # كشف الحواف
            edges = cv2.Canny(gray, 50, 150)

            # البحث عن مستطيلات
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            phone_count = 0
            for contour in contours:
                # تقريب الشكل
                epsilon = 0.02 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)

                # إذا كان مستطيل
                if len(approx) == 4:
                    x, y, w, h = cv2.boundingRect(approx)
                    aspect_ratio = w / h

                    # نسبة العرض للارتفاع مناسبة للهاتف
                    if 0.4 < aspect_ratio < 0.8 and 20 < w < 100 and 30 < h < 150:
                        phone_count += 1

            return min(phone_count / 10.0, 1.0)  # تطبيع

        except Exception as e:
            logger.error(f"خطأ في كشف الهواتف: {e}")
            return 0.0

    def _basic_face_detection(self, frame) -> Dict[str, Any]:
        """كشف وجه أساسي باستخدام OpenCV"""
        try:
            analysis = {
                'face_emotions': {},
                'multiple_faces': 0,
                'crowd_reactions': {}
            }

            if self.face_cascade is None:
                return analysis

            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            faces = self.face_cascade.detectMultiScale(gray, 1.1, 4)

            analysis['multiple_faces'] = len(faces)

            # تحليل أساسي للمشاعر بناءً على حجم الوجه وموضعه
            if len(faces) > 0:
                # أكبر وجه
                largest_face = max(faces, key=lambda x: x[2] * x[3])
                x, y, w, h = largest_face

                # تحليل أساسي جداً
                face_area = w * h
                frame_area = frame.shape[0] * frame.shape[1]
                face_ratio = face_area / frame_area

                # وجه كبير = قريب = ردة فعل محتملة
                if face_ratio > 0.1:
                    analysis['face_emotions'] = {
                        'excitement': min(face_ratio * 5, 1.0),
                        'surprise': min(face_ratio * 3, 1.0)
                    }

            return analysis

        except Exception as e:
            logger.error(f"خطأ في كشف الوجه الأساسي: {e}")
            return {}

    def _basic_movement_detection(self, frame) -> float:
        """كشف حركة أساسي"""
        try:
            # تحويل لرمادي
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            # حساب الحركة بناءً على التغيير في الإطار
            if len(self.movement_history) > 0:
                prev_gray = self.movement_history[-1]
                diff = cv2.absdiff(gray, prev_gray)
                movement = np.mean(diff) / 255.0
            else:
                movement = 0.0

            # حفظ الإطار الحالي
            self.movement_history.append(gray)

            return min(movement * 5, 1.0)

        except Exception as e:
            logger.error(f"خطأ في كشف الحركة الأساسي: {e}")
            return 0.0

    def _find_reaction_in_data(self, frame_data: List[Dict], trigger_time: float) -> Optional[ReactionMoment]:
        """البحث عن ردة فعل في بيانات الإطارات"""
        try:
            if not frame_data:
                return None

            # تحليل البيانات للبحث عن ذروة في النشاط
            timestamps = [d['timestamp'] for d in frame_data]

            # حساب نقاط الشدة لكل إطار
            intensity_scores = []
            for data in frame_data:
                score = 0.0

                # إضافة نقاط من المشاعر
                emotions = data.get('face_emotions', {})
                score += emotions.get('surprise', 0) * 0.3
                score += emotions.get('excitement', 0) * 0.25
                score += emotions.get('shock', 0) * 0.3
                score += emotions.get('joy', 0) * 0.15

                # إضافة نقاط من الحركة
                score += data.get('body_movement', 0) * 0.2

                # إضافة نقاط من ردات الفعل الجماعية
                crowd = data.get('crowd_reactions', {})
                score += crowd.get('applause_probability', 0) * 0.4
                score += crowd.get('cheer_probability', 0) * 0.4
                score += crowd.get('gasp_probability', 0) * 0.3

                # إضافة نقاط من الإيماءات الخاصة
                gestures = data.get('special_gestures', {})
                score += gestures.get('arms_raised', 0) * 0.3
                score += gestures.get('jumping', 0) * 0.4

                # إضافة نقاط من تسجيل الهواتف
                score += data.get('phone_detection', 0) * 0.2

                intensity_scores.append(score)

            # البحث عن ذروة
            if not intensity_scores:
                return None

            max_intensity = max(intensity_scores)
            if max_intensity < self.intensity_threshold:
                return None

            # العثور على نقطة الذروة
            peak_index = intensity_scores.index(max_intensity)
            peak_time = timestamps[peak_index]

            # تحديد بداية ونهاية ردة الفعل
            start_index = peak_index
            end_index = peak_index

            # البحث عن البداية (عندما تنخفض الشدة)
            for i in range(peak_index - 1, -1, -1):
                if intensity_scores[i] < max_intensity * 0.3:
                    break
                start_index = i

            # البحث عن النهاية
            for i in range(peak_index + 1, len(intensity_scores)):
                if intensity_scores[i] < max_intensity * 0.3:
                    break
                end_index = i

            start_time = timestamps[start_index]
            end_time = timestamps[end_index]

            # التأكد من أن المدة مناسبة
            duration = end_time - start_time
            if duration < self.min_reaction_duration or duration > self.max_reaction_duration:
                return None

            # تحديد نوع ردة الفعل
            peak_data = frame_data[peak_index]
            emotion_type = self._determine_reaction_type(peak_data)

            # حساب الثقة
            confidence = min(max_intensity, 1.0)

            return ReactionMoment(
                start_time=start_time,
                peak_time=peak_time,
                end_time=end_time,
                intensity=max_intensity,
                emotion_type=emotion_type,
                confidence=confidence,
                face_changes=peak_data.get('face_emotions', {}),
                body_movement=peak_data.get('body_movement', 0),
                audio_changes={}  # سيتم إضافته لاحقاً
            )

        except Exception as e:
            logger.error(f"خطأ في البحث عن ردة فعل: {e}")
            return None

    def _determine_reaction_type(self, frame_data: Dict) -> str:
        """تحديد نوع ردة الفعل"""
        try:
            emotions = frame_data.get('face_emotions', {})
            crowd = frame_data.get('crowd_reactions', {})
            gestures = frame_data.get('special_gestures', {})
            multiple_faces = frame_data.get('multiple_faces', 0)

            # ردات فعل جماعية
            if multiple_faces >= 3:
                if crowd.get('applause_probability', 0) > 0.6:
                    return 'crowd_applause'
                elif crowd.get('cheer_probability', 0) > 0.6:
                    return 'crowd_cheer'
                elif crowd.get('gasp_probability', 0) > 0.6:
                    return 'crowd_gasp'
                elif crowd.get('laughter_probability', 0) > 0.6:
                    return 'crowd_laughter'

            # إيماءات خاصة
            if gestures.get('jumping', 0) > 0.7:
                return 'jumping_celebration'
            elif gestures.get('arms_raised', 0) > 0.7:
                return 'standing_ovation'

            # تسجيل بالهواتف
            if frame_data.get('phone_detection', 0) > 0.5:
                return 'phone_recording'

            # ردات فعل فردية
            if emotions.get('surprise', 0) > 0.6:
                return 'individual_surprise'
            elif emotions.get('excitement', 0) > 0.6:
                return 'individual_excitement'
            elif emotions.get('shock', 0) > 0.6:
                return 'individual_shock'
            elif emotions.get('joy', 0) > 0.6:
                return 'individual_joy'

            return 'individual_surprise'  # افتراضي

        except Exception as e:
            logger.error(f"خطأ في تحديد نوع ردة الفعل: {e}")
            return 'individual_surprise'

# دوال مساعدة للتوافق مع الأداة الحالية
def detect_reactions_simple(video_path: str, highlight_moments: List[Tuple[float, float]]) -> List[Dict[str, Any]]:
    """دالة مبسطة لكشف ردات الفعل للتوافق مع الأداة الحالية"""
    try:
        detector = AdvancedReactionDetector()
        reactions = detector.detect_reactions_in_video(video_path, highlight_moments)
        
        # تحويل إلى تنسيق مبسط
        simple_reactions = []
        for reaction in reactions:
            simple_reactions.append({
                'start_time': reaction.start_time,
                'end_time': reaction.end_time,
                'type': reaction.emotion_type,
                'intensity': reaction.intensity,
                'confidence': reaction.confidence
            })
        
        return simple_reactions
        
    except Exception as e:
        logger.error(f"خطأ في كشف ردات الفعل البسيط: {e}")
        return []

if __name__ == "__main__":
    # اختبار النظام
    print("🧪 اختبار كاشف ردات الفعل المتقدم")
    print("=" * 50)
    
    detector = AdvancedReactionDetector()
    print(f"✅ تم تهيئة الكاشف")
    print(f"📊 MediaPipe متوفر: {'نعم' if MEDIAPIPE_AVAILABLE else 'لا'}")
    print(f"🎯 أنواع ردات الفعل المدعومة: {len(detector.reaction_types)}")
    
    # عرض أنواع ردات الفعل
    print("\n🎭 أنواع ردات الفعل المدعومة:")
    for key, value in detector.reaction_types.items():
        print(f"   - {key}: {value}")
    
    print("\n🏁 انتهى الاختبار")
