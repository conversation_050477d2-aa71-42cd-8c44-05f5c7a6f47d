#!/usr/bin/env python3
"""
ملف التشغيل السريع لواجهة الويب
AI Youtube Shorts Generator - Web Interface Launcher
"""

import os
import sys
import time
import threading
import webbrowser
from pathlib import Path

def check_environment():
    """فحص البيئة والمتطلبات"""
    print("🔍 فحص البيئة...")
    
    # فحص Python
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        return False
    
    # فحص الملفات المطلوبة
    required_files = [
        'web_app.py',
        'templates/index.html',
        'static/app.js',
        'Components/YoutubeDownloader_fixed.py',
        'Components/Edit.py',
        'Components/Transcription_simple.py'
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ ملف مطلوب غير موجود: {file_path}")
            return False
    
    # فحص المكتبات المطلوبة
    try:
        import flask
        import numpy
        import cv2
        import openai
        from dotenv import load_dotenv
        print("✅ جميع المكتبات متوفرة")
    except ImportError as e:
        print(f"❌ مكتبة مطلوبة غير متوفرة: {e}")
        print("💡 قم بتشغيل: pip install -r requirements_new.txt")
        return False
    
    # فحص ملف .env
    if not Path('.env').exists():
        print("⚠️  ملف .env غير موجود، سيتم إنشاؤه...")
        create_env_file()
    
    return True

def create_env_file():
    """إنشاء ملف .env إذا لم يكن موجوداً"""
    env_content = """# إعدادات AI Youtube Shorts Generator
OPENAI_API=sk-test-key-for-demo-purposes-only
# ضع مفتاح OpenAI API الحقيقي هنا للحصول على الميزات الكاملة
"""
    
    with open('.env', 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print("✅ تم إنشاء ملف .env")

def open_browser_delayed():
    """فتح المتصفح بعد تأخير"""
    time.sleep(2)  # انتظار حتى يبدأ الخادم
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 تم فتح المتصفح تلقائياً")
    except Exception as e:
        print(f"⚠️  لم يتم فتح المتصفح تلقائياً: {e}")
        print("🔗 افتح الرابط يدوياً: http://localhost:5000")

def main():
    """الدالة الرئيسية"""
    print("🚀 AI Youtube Shorts Generator - Web Interface")
    print("=" * 60)
    
    # فحص البيئة
    if not check_environment():
        print("\n❌ فشل في فحص البيئة. يرجى حل المشاكل أعلاه.")
        input("اضغط Enter للخروج...")
        return False
    
    print("✅ البيئة جاهزة!")
    print("\n📋 معلومات التشغيل:")
    print("🌐 الرابط المحلي: http://localhost:5000")
    print("🔗 سيتم فتح المتصفح تلقائياً...")
    print("⏹️  للإيقاف: اضغط Ctrl+C في هذه النافذة")
    print("=" * 60)
    
    # فتح المتصفح في خيط منفصل
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()
    
    # تشغيل التطبيق
    try:
        # استيراد وتشغيل التطبيق
        from web_app import app
        app.run(debug=False, host='0.0.0.0', port=5000, threaded=True)
        
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف الخادم بواسطة المستخدم")
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        print("\n🔧 حلول مقترحة:")
        print("1. تأكد من أن المنفذ 5000 غير مستخدم")
        print("2. تأكد من تثبيت جميع المكتبات المطلوبة")
        print("3. تأكد من وجود جميع الملفات المطلوبة")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ تم إنهاء التطبيق بنجاح")
        else:
            print("\n❌ حدث خطأ أثناء التشغيل")
    except Exception as e:
        print(f"\n💥 خطأ غير متوقع: {e}")
    
    print("\n" + "=" * 60)
    input("اضغط Enter للخروج...")
    sys.exit(0)
